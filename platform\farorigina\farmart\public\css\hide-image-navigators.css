/* Hide Image Navigators on All Platforms */

/* Hide all types of image navigation elements */
.custom-gallery-nav,
.gallery-nav,
.btn-gallery-nav,
.slick-arrow,
.custom-gallery-prev,
.custom-gallery-next,
.gallery-nav-prev,
.gallery-nav-next,
.product-gallery__wrapper .slick-arrow,
.bb-product-gallery-images .slick-arrow,
.bb-product-gallery-thumbnails .slick-arrow,
.slick-prev,
.slick-next,
.slick-prev-arrow,
.slick-next-arrow,
.btn-prev,
.btn-next,
.custom-gallery-nav.prev,
.custom-gallery-nav.next {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Hide navigation on hover states */
.product-gallery__wrapper:hover .slick-arrow,
.custom-gallery-main:hover .custom-gallery-nav,
.bb-product-gallery-images:hover .slick-arrow {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Make product image section smaller on desktop only */
@media (min-width: 992px) {
    /* Main product gallery containers */
    .product-gallery__wrapper {
        max-width: calc(100% - 120px) !important;
    }
    
    .custom-gallery-container {
        max-width: 70% !important;
    }
    
    .custom-gallery-main {
        max-width: 350px !important;
        max-height: 350px !important;
    }

    .bb-product-gallery-images {
        max-width: 350px !important;
        max-height: 350px !important;
    }
    
    .bb-product-gallery {
        max-width: 70% !important;
    }

    /* Product gallery wrapper sizing */
    .product-gallery {
        max-width: 70% !important;
    }

    .product-gallery .product-gallery__wrapper {
        max-width: calc(70% - 80px) !important;
    }
}

/* Ensure navigation stays hidden on mobile too */
@media (max-width: 991px) {
    .custom-gallery-nav,
    .gallery-nav,
    .btn-gallery-nav,
    .slick-arrow {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }
}

/* Additional specific selectors for stubborn navigation elements */
[class*="gallery-nav"],
[class*="slick-arrow"],
[class*="custom-gallery-nav"],
[class*="btn-gallery"],
button[onclick*="prev()"],
button[onclick*="next()"],
button[onclick*="customGallery"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Hide any remaining arrow elements */
.svg-icon svg use[href*="chevron"],
.svg-icon svg use[xlink\\:href*="chevron"],
.svg-icon svg use[href*="arrow"],
.svg-icon svg use[xlink\\:href*="arrow"] {
    display: none !important;
}

/* Ensure parent containers of hidden arrows are also hidden */
span.slick-prev-arrow,
span.slick-next-arrow,
.slick-arrow span {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}
