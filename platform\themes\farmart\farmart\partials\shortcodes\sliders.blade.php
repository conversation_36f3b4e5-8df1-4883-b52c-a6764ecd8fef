<style>
/* Custom HD Slider Styles - Full Width to Match Components */
.custom-hd-slider {
    position: relative;
    width: 100%;
    margin: 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    background: #f8f9fa;
}

.custom-hd-slider-container {
    position: relative;
    width: 100%;
    height: 260px;
    overflow: hidden;
}

.custom-hd-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
    transform: translateX(100%);
}

.custom-hd-slide.active {
    opacity: 1;
    transform: translateX(0);
    z-index: 2;
}

.custom-hd-slide.prev {
    transform: translateX(-100%);
}

.custom-hd-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    transition: transform 0.3s ease;
}

.custom-hd-slide:hover img {
    transform: scale(1.02);
}

/* Navigation Dots - Minimal Design */
.custom-hd-dots {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 10;
}

.custom-hd-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.custom-hd-dot.active {
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1.25);
}

/* Navigation Arrows - Hidden */
.custom-hd-nav {
    display: none !important;
}

/* Responsive Design - Full Width */
@media (max-width: 1200px) {
    .custom-hd-slider-container {
        height: 220px;
    }
}

@media (max-width: 768px) {
    .custom-hd-slider {
        border-radius: 6px;
        margin: 0;
    }
    .custom-hd-slider-container {
        height: 180px;
    }
    .custom-hd-dots {
        bottom: 12px;
        gap: 6px;
    }
    .custom-hd-dot {
        width: 6px;
        height: 6px;
    }
}

@media (max-width: 480px) {
    .custom-hd-slider {
        border-radius: 4px;
    }
    .custom-hd-slider-container {
        height: 140px;
    }
    .custom-hd-dots {
        bottom: 10px;
        gap: 5px;
    }
    .custom-hd-dot {
        width: 5px;
        height: 5px;
    }
}

/* Loading Animation */
.custom-hd-slider.loading .custom-hd-slide img {
    filter: blur(2px);
    transition: filter 0.3s ease;
}

.custom-hd-slider.loaded .custom-hd-slide img {
    filter: blur(0);
}

/* Section Wrapper - Match Other Components Exactly */
.section-content__slider {
    background: none !important;
    background-color: #ffffff !important;
    padding: 15px 0 !important;
    margin: 0 !important;
}

.section-content__slider .container-xxxl {
    max-width: 1320px !important;
    margin: 0 auto !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

@media (max-width: 1400px) {
    .section-content__slider .container-xxxl {
        max-width: 1140px !important;
    }
}

@media (max-width: 1200px) {
    .section-content__slider .container-xxxl {
        max-width: 960px !important;
    }
}

@media (max-width: 992px) {
    .section-content__slider .container-xxxl {
        max-width: 720px !important;
    }
}

@media (max-width: 768px) {
    .section-content__slider .container-xxxl {
        max-width: 540px !important;
        padding-left: 12px !important;
        padding-right: 12px !important;
    }
}

@media (max-width: 576px) {
    .section-content__slider .container-xxxl {
        max-width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
}
</style>
@if (count($sliders) > 0)
    @php
        $sliders->loadMissing('metadata');
        $autoplay = ($shortcode->is_autoplay ?: 'yes') == 'yes';
        $autoplaySpeed = in_array($shortcode->autoplay_speed, theme_get_autoplay_speed_options()) ? $shortcode->autoplay_speed : 5000;
        $sliderId = 'custom-hd-slider-' . uniqid();
    @endphp
    <div class="section-content section-content__slider">
        <div class="container-xxxl">
            <div class="row gx-0 gx-md-4">
                <div class="@if (is_plugin_active('ads') && $shortcode->ads) col-md-8 @else col-md-12 @endif">
                    <!-- Custom HD Slider -->
                    <div class="custom-hd-slider loading" id="{{ $sliderId }}">
                        <div class="custom-hd-slider-container">
                            @foreach ($sliders as $index => $slider)
                                <div class="custom-hd-slide @if($index === 0) active @endif" data-slide="{{ $index }}">
                                    @if ($slider->link)
                                        <a href="{{ url($slider->link) }}" title="{{ $slider->title }}">
                                    @endif
                                    @php
                                        $tabletImage = $slider->getMetaData('tablet_image', true) ?: $slider->image;
                                        $mobileImage = $slider->getMetaData('mobile_image', true) ?: $tabletImage;
                                    @endphp
                                    <picture>
                                        <source
                                            srcset="{{ RvMedia::getImageUrl($slider->image, null, false, RvMedia::getDefaultImage()) }}"
                                            media="(min-width: 1200px)"
                                        />
                                        <source
                                            srcset="{{ RvMedia::getImageUrl($tabletImage, null, false, RvMedia::getDefaultImage()) }}"
                                            media="(min-width: 768px)"
                                        />
                                        <source
                                            srcset="{{ RvMedia::getImageUrl($mobileImage, null, false, RvMedia::getDefaultImage()) }}"
                                            media="(max-width: 767px)"
                                        />
                                        <img
                                            src="{{ RvMedia::getImageUrl($slider->image, null, false, RvMedia::getDefaultImage()) }}"
                                            alt="{{ $slider->title ?: 'Slider Image ' . ($index + 1) }}"
                                            loading="{{ $index === 0 ? 'eager' : 'lazy' }}"
                                            onload="this.parentElement.parentElement.parentElement.parentElement.classList.add('loaded')"
                                        />
                                    </picture>
                                    @if ($slider->link)
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        </div>

                        @if (count($sliders) > 1)
                            <!-- Navigation Dots -->
                            <div class="custom-hd-dots">
                                @foreach ($sliders as $index => $slider)
                                    <div class="custom-hd-dot @if($index === 0) active @endif"
                                         onclick="customHDSlider.goTo('{{ $sliderId }}', {{ $index }})"
                                         data-slide="{{ $index }}"
                                         aria-label="Go to slide {{ $index + 1 }}"></div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
                @if (is_plugin_active('ads') && $shortcode->ads)
                    <div class="col-md-4">
                        <div class="section-banner-wrapper" style="height: 100%; margin: 0 !important; padding: 0 !important;">
                            <div class="banner-medium" style="height: 100%;">
                                <div class="banner-item__image" style="height: 260px; display: flex; align-items: center; justify-content: center; border-radius: 12px; overflow: hidden;">
                                    {!! display_ads_advanced($shortcode->ads) !!}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Custom HD Slider JavaScript -->
    <script>
    window.customHDSlider = window.customHDSlider || {
        sliders: {},

        init: function(sliderId, autoplay = false, autoplaySpeed = 5000) {
            const slider = document.getElementById(sliderId);
            if (!slider) return;

            const slides = slider.querySelectorAll('.custom-hd-slide');
            const dots = slider.querySelectorAll('.custom-hd-dot');

            this.sliders[sliderId] = {
                currentSlide: 0,
                totalSlides: slides.length,
                autoplay: autoplay,
                autoplaySpeed: autoplaySpeed,
                autoplayTimer: null,
                isTransitioning: false
            };

            // Start autoplay if enabled
            if (autoplay && slides.length > 1) {
                this.startAutoplay(sliderId);
            }

            // Pause autoplay on hover
            slider.addEventListener('mouseenter', () => this.pauseAutoplay(sliderId));
            slider.addEventListener('mouseleave', () => this.resumeAutoplay(sliderId));

            // Touch/swipe support
            this.addTouchSupport(sliderId);
        },

        goTo: function(sliderId, slideIndex) {
            const sliderData = this.sliders[sliderId];
            if (!sliderData || sliderData.isTransitioning) return;

            const slider = document.getElementById(sliderId);
            const slides = slider.querySelectorAll('.custom-hd-slide');
            const dots = slider.querySelectorAll('.custom-hd-dot');

            if (slideIndex < 0 || slideIndex >= sliderData.totalSlides) return;

            sliderData.isTransitioning = true;

            // Remove active classes
            slides[sliderData.currentSlide].classList.remove('active');
            dots[sliderData.currentSlide]?.classList.remove('active');

            // Add active classes
            slides[slideIndex].classList.add('active');
            dots[slideIndex]?.classList.add('active');

            sliderData.currentSlide = slideIndex;

            // Reset transition flag
            setTimeout(() => {
                sliderData.isTransitioning = false;
            }, 800);

            // Reset autoplay timer
            if (sliderData.autoplay) {
                this.resetAutoplay(sliderId);
            }
        },

        next: function(sliderId) {
            const sliderData = this.sliders[sliderId];
            if (!sliderData) return;

            const nextSlide = (sliderData.currentSlide + 1) % sliderData.totalSlides;
            this.goTo(sliderId, nextSlide);
        },

        prev: function(sliderId) {
            const sliderData = this.sliders[sliderId];
            if (!sliderData) return;

            const prevSlide = sliderData.currentSlide === 0 ? sliderData.totalSlides - 1 : sliderData.currentSlide - 1;
            this.goTo(sliderId, prevSlide);
        },

        startAutoplay: function(sliderId) {
            const sliderData = this.sliders[sliderId];
            if (!sliderData || !sliderData.autoplay) return;

            sliderData.autoplayTimer = setInterval(() => {
                this.next(sliderId);
            }, sliderData.autoplaySpeed);
        },

        pauseAutoplay: function(sliderId) {
            const sliderData = this.sliders[sliderId];
            if (!sliderData || !sliderData.autoplayTimer) return;

            clearInterval(sliderData.autoplayTimer);
            sliderData.autoplayTimer = null;
        },

        resumeAutoplay: function(sliderId) {
            const sliderData = this.sliders[sliderId];
            if (!sliderData || !sliderData.autoplay || sliderData.autoplayTimer) return;

            this.startAutoplay(sliderId);
        },

        resetAutoplay: function(sliderId) {
            this.pauseAutoplay(sliderId);
            this.resumeAutoplay(sliderId);
        },

        addTouchSupport: function(sliderId) {
            const slider = document.getElementById(sliderId);
            let startX = 0;
            let startY = 0;
            let endX = 0;
            let endY = 0;

            slider.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            slider.addEventListener('touchend', (e) => {
                endX = e.changedTouches[0].clientX;
                endY = e.changedTouches[0].clientY;

                const deltaX = endX - startX;
                const deltaY = endY - startY;

                // Only trigger if horizontal swipe is more significant than vertical
                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                    if (deltaX > 0) {
                        this.prev(sliderId);
                    } else {
                        this.next(sliderId);
                    }
                }
            });
        }
    };

    // Initialize this slider
    document.addEventListener('DOMContentLoaded', function() {
        customHDSlider.init('{{ $sliderId }}', {{ $autoplay ? 'true' : 'false' }}, {{ $autoplaySpeed }});
    });
    </script>
@endif
