<?php

namespace Bo<PERSON>ble\PayStation\Providers;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Botble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Forms\PayStationPaymentMethodForm;
use Botble\PayStation\Services\Gateways\PayStationPaymentService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerPayStationMethod'], 11, 2);
        add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithPayStation'], 11, 2);

        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 93);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == PaymentMethodEnum::class) {
                $values['PAYSTATION'] = PAYSTATION_PAYMENT_METHOD_NAME;
            }

            return $values;
        }, 20, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == PAYSTATION_PAYMENT_METHOD_NAME) {
                $value = 'PayStation';
            }

            return $value;
        }, 20, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == PAYSTATION_PAYMENT_METHOD_NAME) {
                $value = Html::tag(
                    'span',
                    PaymentMethodEnum::getLabel($value),
                    ['class' => 'label-success status-label']
                )
                    ->toHtml();
            }

            return $value;
        }, 20, 2);

        add_filter(PAYMENT_FILTER_GET_SERVICE_CLASS, function ($data, $value) {
            if ($value == PAYSTATION_PAYMENT_METHOD_NAME) {
                $data = PayStationPaymentService::class;
            }

            return $data;
        }, 20, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . PayStationPaymentMethodForm::create()->renderForm();
    }

    public function registerPayStationMethod(?string $html, array $data): string
    {
        $merchantId = get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME);
        $password = get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME);

        if (! $merchantId || ! $password) {
            return $html;
        }

        $data['errorMessage'] = null;

        PaymentMethods::method(PAYSTATION_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/paystation::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithPayStation(array $data, Request $request): array
    {
        if ($data['type'] !== PAYSTATION_PAYMENT_METHOD_NAME) {
            return $data;
        }

        $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

        $merchantId = get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME);
        $password = get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME);
        $chargeForCustomer = get_payment_setting('charge_for_customer', PAYSTATION_PAYMENT_METHOD_NAME, '1');
        $emi = get_payment_setting('emi', PAYSTATION_PAYMENT_METHOD_NAME, '0');

        if (! $merchantId || ! $password) {
            $data['error'] = true;
            $data['message'] = __('PayStation payment method is not configured properly.');
            Log::error('PayStation: Missing configuration', [
                'merchant_id_set' => !empty($merchantId),
                'password_set' => !empty($password)
            ]);
            return $data;
        }

        $orderIds = $paymentData['order_id'];
        $amount = $paymentData['amount'];

        // Check EMI minimum amount requirement
        if ($emi == '1' && $amount < 5000) {
            $data['error'] = true;
            $data['message'] = __('Minimum amount should be 5000 BDT for EMI. Please choose a higher amount for EMI.');
            return $data;
        }

        $invoiceNumber = 'LV' . $merchantId . '-' . time() . '-' . Arr::first($orderIds);

        // Try to build callback URL safely
        try {
            $callbackUrl = route('payments.paystation.callback');
        } catch (Exception $e) {
            $callbackUrl = url('/payment/paystation/callback');
        }

        // Extract customer information more safely
        $customerName = 'Customer';
        $customerPhone = '';
        $customerEmail = '';
        $customerAddress = '';

        if (isset($paymentData['address'])) {
            $address = $paymentData['address'];
            $customerName = ($address['first_name'] ?? '') . ' ' . ($address['last_name'] ?? '');
            $customerPhone = $address['phone'] ?? '';
            $customerEmail = $address['email'] ?? '';
            $customerAddress = $address['address'] ?? $address['address_1'] ?? '';
        }

        $requestData = [
            'invoice_number' => $invoiceNumber,
            'currency' => 'BDT',
            'payment_amount' => $amount,
            'cust_name' => trim($customerName) ?: 'Customer',
            'cust_phone' => $customerPhone,
            'cust_email' => $customerEmail,
            'cust_address' => $customerAddress,
            'reference' => 'Laravel-Ecommerce',
            'callback_url' => $callbackUrl,
            'checkout_items' => 'items',
            'pay_with_charge' => $chargeForCustomer,
            'emi' => $emi,
            'merchantId' => $merchantId,
            'password' => $password
        ];

        try {
            Log::info('PayStation: Initiating payment', [
                'invoice_number' => $invoiceNumber,
                'amount' => $amount,
                'customer_name' => $requestData['cust_name']
            ]);

            do_action('payment_before_making_api_request', PAYSTATION_PAYMENT_METHOD_NAME, $requestData);

            $response = Http::timeout(60)->post('https://api.paystation.com.bd/initiate-payment', $requestData);

            if ($response->failed()) {
                $data['error'] = true;
                $data['message'] = __('Payment error: ') . $response->body();
                Log::error('PayStation: API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return $data;
            }

            $result = $response->json();

            Log::info('PayStation: API response received', $result);

            do_action('payment_after_api_response', PAYSTATION_PAYMENT_METHOD_NAME, $requestData, $result);

            // Check response according to official API documentation
            if (isset($result['status_code']) && $result['status_code'] === '200' &&
                isset($result['status']) && $result['status'] === 'success') {

                // Store payment information for later processing
                PaymentHelper::storeLocalPayment([
                    'amount' => $amount,
                    'currency' => 'BDT',
                    'charge_id' => $invoiceNumber,
                    'order_id' => $orderIds,
                    'customer_id' => $paymentData['customer_id'] ?? null,
                    'customer_type' => $paymentData['customer_type'] ?? null,
                    'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                    'status' => PaymentStatusEnum::PENDING,
                ]);

                // Set success response with checkout URL for external redirect
                $data['charge_id'] = $invoiceNumber;
                $data['checkoutUrl'] = $result['payment_url'];

                Log::info('PayStation: Setting checkout URL for redirect', ['url' => $result['payment_url']]);

            } else {
                $data['error'] = true;
                $data['message'] = __('Payment failed: ') . ($result['message'] ?? 'Unknown error');
                Log::error('PayStation: Payment initiation failed', [
                    'status_code' => $result['status_code'] ?? 'unknown',
                    'status' => $result['status'] ?? 'unknown',
                    'message' => $result['message'] ?? 'No message'
                ]);
            }

        } catch (Exception $exception) {
            $data['error'] = true;
            $data['message'] = __('Payment failed: ') . $exception->getMessage();
            Log::error('PayStation: Exception during payment', [
                'exception' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
            ]);
        }

        return $data;
    }
}
