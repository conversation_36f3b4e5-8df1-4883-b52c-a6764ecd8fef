<?php

echo "PayStation Live Server Callback Debug\n";
echo "====================================\n\n";

// Load Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "1. Checking Callback URL Accessibility:\n";

$baseUrl = config('app.url') ?: 'https://yourdomain.com';
$callbackUrl = $baseUrl . '/payment/paystation/callback';

echo "   Expected callback URL: $callbackUrl\n";

// Test if callback URL is accessible
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $callbackUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'PayStation-Test');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "   ✗ Callback URL not accessible: $error\n";
} else {
    echo "   ✓ Callback URL accessible (HTTP $httpCode)\n";
    if ($httpCode == 400) {
        echo "     (HTTP 400 is expected for GET request - callback expects POST)\n";
    }
}

echo "\n2. Checking Recent Payment Records:\n";

try {
    // Check recent payments
    $payments = DB::table('payments')
        ->where('payment_channel', 'paystation')
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    if ($payments->count() > 0) {
        echo "   ✓ Found " . $payments->count() . " PayStation payments:\n";
        foreach ($payments as $payment) {
            echo "     - ID: {$payment->id}, Charge: {$payment->charge_id}, Status: {$payment->status}\n";
            echo "       Amount: {$payment->amount}, Created: {$payment->created_at}\n";
        }
    } else {
        echo "   ✗ No PayStation payment records found\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking payments: " . $e->getMessage() . "\n";
}

echo "\n3. Checking Recent Orders:\n";

try {
    // Check recent orders
    $orders = DB::table('ec_orders')
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    if ($orders->count() > 0) {
        echo "   ✓ Found " . $orders->count() . " recent orders:\n";
        foreach ($orders as $order) {
            echo "     - ID: {$order->id}, Status: {$order->status}, Amount: {$order->amount}\n";
            echo "       Token: {$order->token}, Finished: " . ($order->is_finished ? 'Yes' : 'No') . "\n";
            echo "       Payment ID: " . ($order->payment_id ?: 'None') . ", Created: {$order->created_at}\n";
        }
    } else {
        echo "   ✗ No orders found\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking orders: " . $e->getMessage() . "\n";
}

echo "\n4. Checking Laravel Logs for PayStation Activity:\n";

$logFile = 'storage/logs/laravel-' . date('Y-m-d') . '.log';
if (file_exists($logFile)) {
    echo "   ✓ Checking log file: $logFile\n";
    
    // Get PayStation related log entries
    $command = "grep -i 'paystation' \"$logFile\" | tail -10";
    $output = shell_exec($command);
    
    if ($output) {
        echo "   Recent PayStation log entries:\n";
        $lines = explode("\n", trim($output));
        foreach ($lines as $line) {
            if (trim($line)) {
                echo "     " . trim($line) . "\n";
            }
        }
    } else {
        echo "   ✗ No PayStation entries in today's log\n";
    }
} else {
    echo "   ✗ Today's log file not found\n";
}

echo "\n5. Testing Callback Processing Logic:\n";

// Simulate callback with your actual invoice number from logs
$testInvoice = 'LV1389-1752255805-1752783970-74'; // From your previous logs
echo "   Testing with invoice: $testInvoice\n";

try {
    // Check if payment record exists
    $payment = DB::table('payments')
        ->where('charge_id', $testInvoice)
        ->first();
    
    if ($payment) {
        echo "   ✓ Found payment record for this invoice\n";
        echo "     Payment ID: {$payment->id}, Order ID: {$payment->order_id}\n";
        
        // Check if order exists
        $orderIds = json_decode($payment->order_id, true) ?: [$payment->order_id];
        $order = DB::table('ec_orders')
            ->whereIn('id', $orderIds)
            ->first();
        
        if ($order) {
            echo "   ✓ Found order: ID {$order->id}, Status: {$order->status}\n";
        } else {
            echo "   ✗ Order not found for payment\n";
        }
    } else {
        echo "   ✗ No payment record found for this invoice\n";
        
        // Try extracting order ID from invoice
        $parts = explode('-', $testInvoice);
        $orderId = end($parts);
        echo "   Trying order ID extraction: $orderId\n";
        
        $order = DB::table('ec_orders')->where('id', $orderId)->first();
        if ($order) {
            echo "   ✓ Found order by ID extraction: {$order->id}\n";
        } else {
            echo "   ✗ No order found by ID extraction either\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Error in callback simulation: " . $e->getMessage() . "\n";
}

echo "\n6. Checking PayStation Configuration:\n";

try {
    $merchantId = get_payment_setting('merchant_id', 'paystation');
    $password = get_payment_setting('password', 'paystation');
    $status = get_payment_setting('status', 'paystation');
    
    echo "   Merchant ID: " . ($merchantId ? 'SET' : 'NOT SET') . "\n";
    echo "   Password: " . ($password ? 'SET' : 'NOT SET') . "\n";
    echo "   Status: " . ($status ? 'ENABLED' : 'DISABLED') . "\n";
} catch (Exception $e) {
    echo "   ✗ Error checking PayStation config: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "DIAGNOSIS:\n";
echo "==========\n";

echo "\n🔍 MOST LIKELY ISSUES:\n";
echo "1. PayStation callback URL not configured in PayStation dashboard\n";
echo "2. PayStation not sending callbacks after payment completion\n";
echo "3. Callback URL blocked by firewall/security\n";
echo "4. Orders created but payment not linked properly\n";
echo "5. PAYMENT_ACTION_PAYMENT_PROCESSED not triggering\n";

echo "\n🔧 IMMEDIATE ACTIONS:\n";
echo "1. Configure callback URL in PayStation merchant dashboard:\n";
echo "   $callbackUrl\n";
echo "2. Check if PayStation is actually sending callbacks\n";
echo "3. Monitor logs during a test payment\n";
echo "4. Verify server can receive external POST requests\n";

echo "\n📋 TESTING STEPS:\n";
echo "1. Make a small test payment\n";
echo "2. Immediately check logs: tail -f storage/logs/laravel-" . date('Y-m-d') . ".log\n";
echo "3. Look for 'PayStation: Callback received' messages\n";
echo "4. If no callback received, check PayStation dashboard configuration\n";
echo "5. If callback received but order not created, check the processing logic\n";

echo "\nDebug completed!\n";
