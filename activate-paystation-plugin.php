<?php

echo "PayStation Payment Gateway Plugin Setup\n";
echo "=====================================\n\n";

// Check if the plugin directory exists
$pluginDir = __DIR__ . '/platform/plugins/paystation';
if (!is_dir($pluginDir)) {
    echo "Error: PayStation plugin directory not found at: $pluginDir\n";
    exit(1);
}

echo "✓ PayStation plugin directory found\n";

// Check if required files exist
$requiredFiles = [
    'plugin.json',
    'src/Providers/PayStationServiceProvider.php',
    'src/Providers/HookServiceProvider.php',
    'src/Services/Gateways/PayStationPaymentService.php',
    'src/Forms/PayStationPaymentMethodForm.php',
    'src/Http/Controllers/PayStationController.php',
    'helpers/constants.php',
    'routes/web.php',
    'resources/views/methods.blade.php',
    'resources/views/instructions.blade.php'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    $filePath = $pluginDir . '/' . $file;
    if (!file_exists($filePath)) {
        $missingFiles[] = $file;
    }
}

if (!empty($missingFiles)) {
    echo "Error: Missing required files:\n";
    foreach ($missingFiles as $file) {
        echo "  - $file\n";
    }
    exit(1);
}

echo "✓ All required plugin files found\n";

// Create public assets directory if it doesn't exist
$publicDir = __DIR__ . '/public/vendor/core/plugins/paystation';
if (!is_dir($publicDir)) {
    mkdir($publicDir, 0755, true);
    echo "✓ Created public assets directory\n";
}

// Create images directory
$imagesDir = $publicDir . '/images';
if (!is_dir($imagesDir)) {
    mkdir($imagesDir, 0755, true);
    echo "✓ Created images directory\n";
}

echo "\nPayStation Plugin Setup Complete!\n";
echo "=================================\n\n";

echo "Next Steps:\n";
echo "1. Add PayStation logo image to: public/vendor/core/plugins/paystation/images/paystation.png\n";
echo "2. Go to Admin Panel > Plugins and activate the PayStation plugin\n";
echo "3. Configure PayStation settings in Admin Panel > Plugins > Payment\n";
echo "4. Enter your PayStation Merchant ID and Password\n";
echo "5. Configure charge settings and EMI options\n";
echo "6. Set up fail/cancel URL\n";
echo "7. Test the payment integration\n\n";

echo "PayStation API URLs:\n";
echo "- Payment Initiation: https://api.paystation.com.bd/initiate-payment\n";
echo "- Transaction Status: https://api.paystation.com.bd/transaction-status\n\n";

echo "Callback URLs (configure these in PayStation dashboard):\n";
echo "- Callback URL: {your-domain}/payment/paystation/callback\n";
echo "- Webhook URL: {your-domain}/payment/paystation/webhook\n\n";

echo "Note: PayStation only supports Bangladesh Taka (BDT) currency.\n";
echo "EMI option requires minimum 5000 BDT amount.\n";
