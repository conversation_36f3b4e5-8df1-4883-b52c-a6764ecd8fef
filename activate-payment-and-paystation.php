<?php

require_once 'vendor/autoload.php';

echo "Payment & PayStation Plugin Activation\n";
echo "======================================\n\n";

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    $pluginService = app('Botble\PluginManagement\Services\PluginService');
    
    echo "1. Activating Payment plugin first...\n";
    
    // First activate the payment plugin
    try {
        $result = $pluginService->activate('payment');
        if ($result['error']) {
            echo "   ✗ Payment plugin activation failed: " . $result['message'] . "\n";
        } else {
            echo "   ✓ Payment plugin activated successfully!\n";
        }
    } catch (Exception $e) {
        echo "   ✗ Payment plugin activation error: " . $e->getMessage() . "\n";
    }
    
    echo "\n2. Activating PayStation plugin...\n";
    
    // Now activate PayStation plugin
    try {
        $result = $pluginService->activate('paystation');
        if ($result['error']) {
            echo "   ✗ PayStation plugin activation failed: " . $result['message'] . "\n";
        } else {
            echo "   ✓ PayStation plugin activated successfully!\n";
        }
    } catch (Exception $e) {
        echo "   ✗ PayStation plugin activation error: " . $e->getMessage() . "\n";
    }
    
    echo "\n3. Clearing caches...\n";
    
    try {
        Artisan::call('optimize:clear');
        echo "   ✓ Application caches cleared\n";
    } catch (Exception $e) {
        echo "   ✗ Cache clear failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. Verifying activation...\n";
    
    // Check activated plugins
    $activatedPlugins = setting('activated_plugins', []);
    
    if (in_array('payment', $activatedPlugins)) {
        echo "   ✓ Payment plugin is activated\n";
    } else {
        echo "   ✗ Payment plugin is not activated\n";
    }
    
    if (in_array('paystation', $activatedPlugins)) {
        echo "   ✓ PayStation plugin is activated\n";
    } else {
        echo "   ✗ PayStation plugin is not activated\n";
    }
    
    echo "\n5. Testing PayStation integration...\n";
    
    // Test if constants are available
    $constantsFile = 'platform/plugins/paystation/helpers/constants.php';
    if (file_exists($constantsFile)) {
        include_once $constantsFile;
        if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
            echo "   ✓ PayStation constants loaded\n";
        } else {
            echo "   ✗ PayStation constants not loaded\n";
        }
    }
    
    // Test if helper functions are available
    if (function_exists('get_payment_setting')) {
        echo "   ✓ Payment helper functions available\n";
    } else {
        echo "   ✗ Payment helper functions not available\n";
    }
    
    echo "\nActivation completed!\n";
    echo "\nNext steps:\n";
    echo "1. Go to Admin Panel > Plugins to verify both plugins are active\n";
    echo "2. Go to Admin Panel > Plugins > Payment to configure PayStation\n";
    echo "3. Enter your PayStation Merchant ID and Password\n";
    echo "4. Configure charge settings and EMI options\n";
    echo "5. Set fail/cancel URL\n";
    echo "6. Test the payment integration\n";
    
} catch (Exception $e) {
    echo "Error during activation: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nIf you still get 'Payment failed!' errors after activation:\n";
echo "1. Check that PayStation credentials are correct\n";
echo "2. Ensure your server can make HTTPS requests to PayStation API\n";
echo "3. Check Laravel logs for specific error messages\n";
echo "4. Verify callback URLs are publicly accessible\n";
