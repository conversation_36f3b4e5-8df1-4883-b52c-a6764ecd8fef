# PayStation Payment Gateway - Troubleshooting Guide

## Current Issue: "Payment failed!" Error

**UPDATED**: Plugin now follows official PayStation API documentation exactly.

Based on the diagnostic tests and official API documentation, here are the identified issues and solutions:

## 🔍 **Root Cause Analysis**

### Primary Issue: Plugin Not Activated
- ✗ PayStation plugin is not activated in the system
- ✗ Payment plugin (dependency) is not activated
- ✗ Routes are not registered
- ✗ Service providers are not loaded

### Secondary Issues:
- Database connection issues preventing command-line activation
- Plugin dependencies not resolved

## 🛠️ **Step-by-Step Solution**

### Step 1: Activate Plugins via Admin Panel

**IMPORTANT: You must activate plugins through the web admin panel, not command line**

1. **Access Admin Panel**
   - Go to your website's admin panel
   - Login with admin credentials

2. **Activate Payment Plugin First**
   - Navigate to: `Admin Panel > Plugins`
   - Find "Payment" plugin in the list
   - Click "Activate" button
   - Wait for confirmation

3. **Activate PayStation Plugin**
   - In the same plugins page
   - Find "PayStation Payment Gateway" plugin
   - Click "Activate" button
   - Wait for confirmation

### Step 2: Configure PayStation Settings

1. **Navigate to Payment Settings**
   - Go to: `Admin Panel > Plugins > Payment`
   - Or: `Admin Panel > Settings > Payment`

2. **Configure PayStation**
   - Find PayStation section
   - Enter your **Merchant ID**
   - Enter your **Password**
   - Set **Charge** option (Pay with/without charge)
   - Set **EMI** option (Yes/No)
   - Enter **Fail/Cancel URL**
   - Click "Save"

### Step 3: Verify Configuration

1. **Check Plugin Status**
   - Both Payment and PayStation should show as "Activated"
   - PayStation should appear in payment methods list

2. **Test Settings**
   - Ensure all required fields are filled
   - Verify credentials are correct

## 🔧 **Common Configuration Issues**

### Issue 1: Missing Merchant ID or Password
**Symptoms:** "PayStation payment method is not configured properly"
**Solution:** 
- Get credentials from PayStation dashboard
- Enter in admin panel payment settings

### Issue 2: EMI Minimum Amount Error
**Symptoms:** "Minimum amount should be 5000 BDT for EMI"
**Solution:**
- Either disable EMI option
- Or ensure order amount is ≥ 5000 BDT

### Issue 3: Currency Not Supported
**Symptoms:** Payment fails with currency error
**Solution:**
- PayStation only supports BDT (Bangladesh Taka)
- Set your store currency to BDT

### Issue 4: Callback URL Issues
**Symptoms:** Payment completes but order status doesn't update
**Solution:**
- Ensure your server is publicly accessible
- Configure these URLs in PayStation dashboard:
  - Callback: `https://yourdomain.com/payment/paystation/callback`
  - Webhook: `https://yourdomain.com/payment/paystation/webhook`

## 🧪 **Testing Steps**

### 1. Test PayStation API Directly
```bash
# Test the actual PayStation API with your credentials
php test-paystation-api.php
```

This will test:
- Payment initiation API
- Transaction status API
- API response format validation

### 2. Test Plugin Activation
```bash
# Run this to verify activation
php test-paystation-integration.php
```

Expected output after activation:
- ✓ PayStationServiceProvider is loaded
- ✓ PayStation routes found
- ✓ Helper functions available

### 2. Test Payment Flow
1. Add items to cart
2. Go to checkout
3. Select PayStation as payment method
4. Verify PayStation appears as option
5. Complete test payment with small amount

### 3. Check Logs
Monitor logs during payment:
```bash
tail -f storage/logs/laravel-$(date +%Y-%m-%d).log
```

Look for PayStation-related entries:
- "PayStation: Initiating payment"
- "PayStation: API response received"
- Any error messages

## 🚨 **Error Messages & Solutions**

### "Payment failed!"
**Causes:**
1. Plugin not activated → Activate via admin panel
2. Missing configuration → Set Merchant ID/Password
3. API connectivity issues → Check server outbound HTTPS
4. Invalid credentials → Verify with PayStation

### "PayStation payment method is not configured properly"
**Solution:** Configure Merchant ID and Password in admin panel

### "Minimum amount should be 5000 BDT for EMI"
**Solution:** Disable EMI or increase order amount

### Route not found errors
**Solution:** 
1. Activate plugin via admin panel
2. Clear caches: `php artisan optimize:clear`

## 📋 **Pre-Go-Live Checklist**

- [ ] Payment plugin activated
- [ ] PayStation plugin activated  
- [ ] Merchant ID configured
- [ ] Password configured
- [ ] Charge setting configured
- [ ] EMI setting configured
- [ ] Fail URL configured
- [ ] Currency set to BDT
- [ ] Callback URLs configured in PayStation dashboard
- [ ] Test payment completed successfully
- [ ] Server can make HTTPS requests
- [ ] Callback URLs publicly accessible

## 🔗 **Important URLs**

### PayStation Dashboard
- Production: https://www.paystation.com.bd
- Get your Merchant ID and Password here

### API Endpoints
- Payment Initiation: `https://api.paystation.com.bd/initiate-payment`
- Transaction Status: `https://api.paystation.com.bd/transaction-status`

### Your Callback URLs (configure in PayStation dashboard)
- Callback: `https://yourdomain.com/payment/paystation/callback`
- Webhook: `https://yourdomain.com/payment/paystation/webhook`

## 📞 **Support**

If issues persist after following this guide:

1. **Check Laravel Logs**
   - Look in `storage/logs/` for specific error messages
   - PayStation errors will be prefixed with "PayStation:"

2. **Verify Server Requirements**
   - PHP cURL extension enabled
   - Outbound HTTPS requests allowed
   - Public accessibility for callbacks

3. **Contact PayStation Support**
   - For API-related issues
   - For credential problems
   - For callback configuration help

## 🎯 **Quick Fix Summary**

**Most likely solution for "Payment failed!" error:**

1. Go to Admin Panel > Plugins
2. Activate "Payment" plugin
3. Activate "PayStation Payment Gateway" plugin  
4. Go to Admin Panel > Payment Settings
5. Configure PayStation with your credentials
6. Test with a small amount

This should resolve the payment failure issue in most cases.
