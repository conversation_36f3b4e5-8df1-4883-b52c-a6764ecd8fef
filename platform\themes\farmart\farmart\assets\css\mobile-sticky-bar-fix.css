/* Fix for sticky add to cart bar on mobile and tablet platforms only */
@media (max-width: 991px) {
    /* Ensure the sticky bar is visible on mobile and tablet */
    #sticky-add-to-cart .sticky-atc-wrap,
    .mobile-sticky-add-to-cart {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: fixed !important;
        width: 100% !important;
        transform: none !important;
        background-color: #fff !important;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
        z-index: 998 !important;
    }
    
    /* Position the sticky bar above the footer */
    #sticky-add-to-cart .sticky-atc-wrap {
        bottom: 60px !important;
        left: 0 !important;
        right: 0 !important;
        padding: 5px 0 !important;
        border-bottom: 1px solid #eee !important;
    }
    
    /* Position the mobile sticky bar at the bottom */
    .mobile-sticky-add-to-cart {
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        padding: 5px 0 !important;
        z-index: 999 !important;
    }
    
    /* Add padding to the bottom of the page to prevent content from being hidden */
    .product-detail-container {
        padding-bottom: 120px !important;
    }
    
    /* Style for the sticky add to cart wrapper */
    .sticky-add-to-cart-wrapper {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 0 15px !important;
    }
    
    /* Style for the quantity section */
    .sticky-add-to-cart-wrapper .quantity {
        margin-right: 10px !important;
        min-width: 100px !important;
    }
    
    /* Style for the quantity group */
    .sticky-add-to-cart-wrapper .qty-group {
        display: flex !important;
        align-items: center !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        overflow: hidden !important;
        height: 36px !important;
    }
    
    /* Style for the quantity buttons */
    .sticky-add-to-cart-wrapper .qty-decrease,
    .sticky-add-to-cart-wrapper .qty-increase {
        width: 30px !important;
        height: 36px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: #f5f5f5 !important;
        color: #333 !important;
        text-decoration: none !important;
        font-size: 16px !important;
        font-weight: bold !important;
    }
    
    /* Style for the quantity input */
    .sticky-add-to-cart-wrapper .qty-input {
        width: 40px !important;
        height: 36px !important;
        text-align: center !important;
        border: none !important;
        padding: 0 !important;
        -moz-appearance: textfield !important;
    }
    
    /* Remove spinner from quantity input */
    .sticky-add-to-cart-wrapper .qty-input::-webkit-outer-spin-button,
    .sticky-add-to-cart-wrapper .qty-input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        margin: 0 !important;
    }
    
    /* Style for the action buttons */
    .sticky-add-to-cart-wrapper .action-buttons {
        display: flex !important;
        gap: 10px !important;
        flex: 1 !important;
    }
    
    /* Style for the add to cart button */
    .sticky-add-to-cart-wrapper .btn-add-to-cart {
        flex: 1 !important;
        height: 36px !important;
        background-color: #ff6633 !important;
        color: #fff !important;
        border: none !important;
        border-radius: 4px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
        text-transform: none !important;
    }
    
    /* Style for the buy now button */
    .sticky-add-to-cart-wrapper .btn-buy-now {
        flex: 1 !important;
        height: 36px !important;
        background-color: #ff6b6b !important;
        color: #fff !important;
        border: none !important;
        border-radius: 4px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
        text-transform: none !important;
    }
}
