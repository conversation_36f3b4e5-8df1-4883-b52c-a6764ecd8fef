<?php

use Bo<PERSON>ble\PayStation\Http\Controllers\PayStationController;
use Botble\PayStation\Http\Controllers\PayStationDebugController;
use Illuminate\Support\Facades\Route;

Route::prefix('payment/paystation')
    ->name('payments.paystation.')
    ->group(function () {
        Route::post('webhook', [PayStationController::class, 'webhook'])->name('webhook');

        Route::middleware(['web', 'core'])->group(function () {
            Route::get('callback', [PayStationController::class, 'callback'])->name('callback');
            Route::post('callback', [PayStationController::class, 'callback']);

            // Debug route
            Route::get('debug', [PayStationDebugController::class, 'debugOrderCreation'])->name('debug');
            Route::post('debug', [PayStationDebugController::class, 'debugOrderCreation']);
        });
    });
