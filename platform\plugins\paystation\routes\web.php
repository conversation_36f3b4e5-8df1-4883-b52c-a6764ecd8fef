<?php

use Botble\PayStation\Http\Controllers\PayStationController;
use Illuminate\Support\Facades\Route;

Route::prefix('payment/paystation')
    ->name('payments.paystation.')
    ->group(function () {
        Route::post('webhook', [PayStationController::class, 'webhook'])->name('webhook');

        Route::middleware(['web', 'core'])->group(function () {
            Route::get('callback', [PayStationController::class, 'callback'])->name('callback');
            Route::post('callback', [PayStationController::class, 'callback']);
        });
    });
