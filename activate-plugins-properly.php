<?php

echo "PayStation & Payment Plugin Activation Script\n";
echo "=============================================\n\n";

// Load Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "1. Checking Current Plugin Status:\n";

try {
    // Check current activated plugins
    $activatedPlugins = setting('activated_plugins', []);
    
    echo "   Currently activated plugins:\n";
    if (empty($activatedPlugins)) {
        echo "     - No plugins activated\n";
    } else {
        foreach ($activatedPlugins as $plugin) {
            echo "     - $plugin\n";
        }
    }
    
    $paymentActive = in_array('payment', $activatedPlugins);
    $paystationActive = in_array('paystation', $activatedPlugins);
    
    echo "\n   Payment plugin: " . ($paymentActive ? 'ACTIVATED' : 'NOT ACTIVATED') . "\n";
    echo "   PayStation plugin: " . ($paystationActive ? 'ACTIVATED' : 'NOT ACTIVATED') . "\n";
    
} catch (Exception $e) {
    echo "   ✗ Error checking plugin status: " . $e->getMessage() . "\n";
}

echo "\n2. Activating Payment Plugin:\n";

try {
    $activatedPlugins = setting('activated_plugins', []);
    
    if (!in_array('payment', $activatedPlugins)) {
        $activatedPlugins[] = 'payment';
        setting()->set('activated_plugins', $activatedPlugins)->save();
        echo "   ✓ Payment plugin added to activated list\n";
    } else {
        echo "   ✓ Payment plugin already activated\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error activating payment plugin: " . $e->getMessage() . "\n";
}

echo "\n3. Activating PayStation Plugin:\n";

try {
    $activatedPlugins = setting('activated_plugins', []);
    
    if (!in_array('paystation', $activatedPlugins)) {
        $activatedPlugins[] = 'paystation';
        setting()->set('activated_plugins', $activatedPlugins)->save();
        echo "   ✓ PayStation plugin added to activated list\n";
    } else {
        echo "   ✓ PayStation plugin already activated\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error activating PayStation plugin: " . $e->getMessage() . "\n";
}

echo "\n4. Clearing Caches:\n";

try {
    Artisan::call('config:clear');
    echo "   ✓ Configuration cache cleared\n";
} catch (Exception $e) {
    echo "   ✗ Config clear failed: " . $e->getMessage() . "\n";
}

try {
    Artisan::call('route:clear');
    echo "   ✓ Route cache cleared\n";
} catch (Exception $e) {
    echo "   ✗ Route clear failed: " . $e->getMessage() . "\n";
}

try {
    Artisan::call('view:clear');
    echo "   ✓ View cache cleared\n";
} catch (Exception $e) {
    echo "   ✗ View clear failed: " . $e->getMessage() . "\n";
}

try {
    Artisan::call('optimize:clear');
    echo "   ✓ All caches cleared\n";
} catch (Exception $e) {
    echo "   ✗ Optimize clear failed: " . $e->getMessage() . "\n";
}

echo "\n5. Verifying Activation:\n";

try {
    $activatedPlugins = setting('activated_plugins', []);
    
    if (in_array('payment', $activatedPlugins)) {
        echo "   ✓ Payment plugin is now activated\n";
    } else {
        echo "   ✗ Payment plugin activation failed\n";
    }
    
    if (in_array('paystation', $activatedPlugins)) {
        echo "   ✓ PayStation plugin is now activated\n";
    } else {
        echo "   ✗ PayStation plugin activation failed\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error verifying activation: " . $e->getMessage() . "\n";
}

echo "\n6. Testing Plugin Loading:\n";

// Test if constants are now available
$constantsFile = 'platform/plugins/paystation/helpers/constants.php';
if (file_exists($constantsFile)) {
    include_once $constantsFile;
    
    if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
        echo "   ✓ PayStation constants loaded: " . PAYSTATION_PAYMENT_METHOD_NAME . "\n";
    } else {
        echo "   ✗ PayStation constants not loaded\n";
    }
} else {
    echo "   ✗ PayStation constants file not found\n";
}

// Test if payment constants are available
if (defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
    echo "   ✓ Payment constants loaded: " . PAYMENT_ACTION_PAYMENT_PROCESSED . "\n";
} else {
    echo "   ✗ Payment constants not loaded (may need server restart)\n";
}

echo "\n7. Database Migration Check:\n";

try {
    // Check if payment tables exist
    $paymentTableExists = Schema::hasTable('payments');
    echo "   Payments table exists: " . ($paymentTableExists ? 'YES' : 'NO') . "\n";
    
    if (!$paymentTableExists) {
        echo "   ⚠️  Running payment migrations...\n";
        Artisan::call('migrate', ['--path' => 'platform/plugins/payment/database/migrations']);
        echo "   ✓ Payment migrations completed\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Error checking/running migrations: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "ACTIVATION COMPLETED!\n";
echo "====================\n\n";

echo "✅ NEXT STEPS:\n";
echo "1. Restart your web server (Apache/Nginx)\n";
echo "2. Go to Admin Panel > Plugins to verify activation\n";
echo "3. Configure PayStation settings in Admin Panel > Payment\n";
echo "4. Test the complete payment flow\n\n";

echo "🔧 PAYSTATION CONFIGURATION:\n";
echo "1. Go to Admin Panel > Plugins > Payment\n";
echo "2. Find PayStation section\n";
echo "3. Enter your Merchant ID and Password\n";
echo "4. Set charge and EMI options\n";
echo "5. Configure fail/cancel URL\n";
echo "6. Save settings\n\n";

echo "🌐 PAYSTATION DASHBOARD CONFIGURATION:\n";
echo "Configure these URLs in your PayStation merchant dashboard:\n";
echo "- Callback URL: https://yourdomain.com/payment/paystation/callback\n";
echo "- Webhook URL: https://yourdomain.com/payment/paystation/webhook\n\n";

echo "🧪 TESTING:\n";
echo "1. Make a test purchase with small amount\n";
echo "2. Complete payment on PayStation\n";
echo "3. Check if order appears in admin panel\n";
echo "4. Verify order status is completed\n";
echo "5. Check Laravel logs for any errors\n\n";

echo "If you still have issues after server restart:\n";
echo "- Check Laravel logs in storage/logs/\n";
echo "- Verify callback URL is publicly accessible\n";
echo "- Test with PayStation sandbox first\n";
echo "- Contact PayStation support for callback configuration\n\n";

echo "Activation script completed!\n";
