<?php
// PayStation Plugin Status Checker
// Access this via: http://yourdomain.com/paystation-status.php

// Security: Remove this file after debugging
if (!isset($_GET['debug']) || $_GET['debug'] !== 'paystation') {
    die('Access denied. Use ?debug=paystation to access this page.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>PayStation Plugin Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>PayStation Plugin Status Checker</h1>
    
    <?php
    try {
        // Load Laravel
        require_once '../vendor/autoload.php';
        $app = require_once '../bootstrap/app.php';
        $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
        $kernel->bootstrap();
        
        echo '<div class="status success">✓ Laravel application loaded successfully</div>';
        
        // Check plugin files
        $pluginDir = '../platform/plugins/paystation';
        if (is_dir($pluginDir)) {
            echo '<div class="status success">✓ PayStation plugin directory exists</div>';
        } else {
            echo '<div class="status error">✗ PayStation plugin directory not found</div>';
        }
        
        // Check constants
        $constantsFile = $pluginDir . '/helpers/constants.php';
        if (file_exists($constantsFile)) {
            include_once $constantsFile;
            if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
                echo '<div class="status success">✓ PayStation constants loaded: ' . PAYSTATION_PAYMENT_METHOD_NAME . '</div>';
            } else {
                echo '<div class="status error">✗ PayStation constants not defined</div>';
            }
        } else {
            echo '<div class="status error">✗ PayStation constants file not found</div>';
        }
        
        // Check if plugins are activated
        if (function_exists('setting')) {
            $activatedPlugins = setting('activated_plugins', []);
            
            if (in_array('payment', $activatedPlugins)) {
                echo '<div class="status success">✓ Payment plugin is activated</div>';
            } else {
                echo '<div class="status error">✗ Payment plugin is NOT activated</div>';
            }
            
            if (in_array('paystation', $activatedPlugins)) {
                echo '<div class="status success">✓ PayStation plugin is activated</div>';
            } else {
                echo '<div class="status error">✗ PayStation plugin is NOT activated</div>';
            }
        } else {
            echo '<div class="status warning">⚠ Cannot check plugin activation status (setting function not available)</div>';
        }
        
        // Check helper functions
        if (function_exists('get_payment_setting')) {
            echo '<div class="status success">✓ Payment helper functions available</div>';
            
            // Check PayStation configuration
            if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
                $merchantId = get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME);
                $password = get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME);
                $status = get_payment_setting('status', PAYSTATION_PAYMENT_METHOD_NAME);
                
                echo '<div class="status info">PayStation Configuration:</div>';
                echo '<ul>';
                echo '<li>Merchant ID: ' . ($merchantId ? 'SET' : '<span style="color: red;">NOT SET</span>') . '</li>';
                echo '<li>Password: ' . ($password ? 'SET' : '<span style="color: red;">NOT SET</span>') . '</li>';
                echo '<li>Status: ' . ($status ? 'ENABLED' : '<span style="color: red;">DISABLED</span>') . '</li>';
                echo '</ul>';
            }
        } else {
            echo '<div class="status error">✗ Payment helper functions not available</div>';
        }
        
        // Check routes
        try {
            $router = app('router');
            $routes = $router->getRoutes();
            
            $paystationRoutes = [];
            foreach ($routes as $route) {
                if (strpos($route->getName() ?? '', 'paystation') !== false) {
                    $paystationRoutes[] = $route->getName() . ' -> ' . $route->uri();
                }
            }
            
            if (!empty($paystationRoutes)) {
                echo '<div class="status success">✓ PayStation routes registered:</div>';
                echo '<ul>';
                foreach ($paystationRoutes as $route) {
                    echo '<li>' . htmlspecialchars($route) . '</li>';
                }
                echo '</ul>';
            } else {
                echo '<div class="status error">✗ No PayStation routes found</div>';
            }
        } catch (Exception $e) {
            echo '<div class="status warning">⚠ Could not check routes: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        
        // Check service providers
        try {
            $providers = app()->getLoadedProviders();
            $paystationProvider = 'Botble\\PayStation\\Providers\\PayStationServiceProvider';
            
            if (isset($providers[$paystationProvider])) {
                echo '<div class="status success">✓ PayStationServiceProvider is loaded</div>';
            } else {
                echo '<div class="status error">✗ PayStationServiceProvider is NOT loaded</div>';
            }
        } catch (Exception $e) {
            echo '<div class="status warning">⚠ Could not check service providers: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        
        // Test API connectivity
        echo '<h2>API Connectivity Test</h2>';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.paystation.com.bd/initiate-payment');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($httpCode > 0) {
            echo '<div class="status success">✓ PayStation API is reachable (HTTP ' . $httpCode . ')</div>';
        } else {
            echo '<div class="status error">✗ Cannot reach PayStation API: ' . htmlspecialchars($error) . '</div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="status error">✗ Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    }
    ?>
    
    <h2>Next Steps</h2>
    <div class="status info">
        <strong>If plugins are not activated:</strong>
        <ol>
            <li>Go to Admin Panel > Plugins</li>
            <li>Activate "Payment" plugin first</li>
            <li>Then activate "PayStation Payment Gateway" plugin</li>
            <li>Configure PayStation settings in Payment section</li>
        </ol>
    </div>
    
    <div class="status warning">
        <strong>Security Notice:</strong> Delete this file (paystation-status.php) after debugging!
    </div>
    
</body>
</html>
