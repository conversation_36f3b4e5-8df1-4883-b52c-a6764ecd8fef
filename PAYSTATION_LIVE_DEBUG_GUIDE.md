# PayStation Live Server Debug Guide

## Current Issue: Orders Not Being Created (Payment Working)

Since you mentioned the plugins are activated and you're running on a live server, the issue is likely with the callback process. Here's a systematic debugging approach:

## 🔍 **Step 1: Check if PayStation is Sending Callbacks**

### Option A: Use the Test Callback Script
1. **Temporarily configure this URL in PayStation dashboard:**
   ```
   https://yourdomain.com/test-callback.php
   ```

2. **Make a test payment** and check the log file:
   ```bash
   cat storage/logs/paystation-callback-test.log
   ```

3. **If no log entries appear**, PayStation is not sending callbacks to your server.

### Option B: Monitor Laravel Logs
1. **Watch logs in real-time:**
   ```bash
   tail -f storage/logs/laravel-$(date +%Y-%m-%d).log | grep -i paystation
   ```

2. **Make a test payment** and look for:
   - `PayStation: Callback received`
   - `PayStation: Starting order lookup`
   - `PayStation: Payment processed action completed`

## 🔧 **Step 2: Run the Debug Script**

```bash
php debug-live-callback.php
```

This will check:
- ✅ Callback URL accessibility
- ✅ Recent payment records
- ✅ Recent order records
- ✅ PayStation configuration
- ✅ Log entries

## 🎯 **Step 3: Most Likely Issues & Solutions**

### Issue 1: PayStation Dashboard Not Configured
**Symptoms:** No callback logs appear
**Solution:** 
1. Login to PayStation merchant dashboard
2. Configure callback URL: `https://yourdomain.com/payment/paystation/callback`
3. Save settings

### Issue 2: Server Firewall Blocking Callbacks
**Symptoms:** PayStation configured but no callbacks received
**Solution:**
1. Check server firewall settings
2. Ensure port 80/443 allows incoming connections
3. Whitelist PayStation IP addresses (ask PayStation support)

### Issue 3: Orders Created But Not Linked to Payments
**Symptoms:** Orders exist but payment_id is null
**Solution:** Check if `PAYMENT_ACTION_PAYMENT_PROCESSED` is triggering properly

### Issue 4: Payment Records Not Found
**Symptoms:** Callback received but "Order not found" error
**Solution:** Check if payment records are being created during checkout

## 📋 **Step 4: Detailed Debugging Process**

### 4.1 Check Payment Creation During Checkout
1. **Monitor logs during checkout:**
   ```bash
   tail -f storage/logs/laravel-$(date +%Y-%m-%d).log | grep -E "(PayStation|payment)"
   ```

2. **Look for these log entries:**
   - `PayStation: Initiating payment`
   - `PayStation: API response received`
   - `PayStation: Setting checkout URL for redirect`

### 4.2 Check Database Records
```sql
-- Check recent payments
SELECT * FROM payments WHERE payment_channel = 'paystation' ORDER BY created_at DESC LIMIT 5;

-- Check recent orders
SELECT id, status, amount, token, is_finished, payment_id, created_at 
FROM ec_orders ORDER BY created_at DESC LIMIT 5;

-- Check if orders have payment_id
SELECT COUNT(*) as orders_without_payment 
FROM ec_orders WHERE payment_id IS NULL AND created_at > DATE_SUB(NOW(), INTERVAL 1 DAY);
```

### 4.3 Test Callback Manually
Create a test callback request:
```bash
curl -X POST "https://yourdomain.com/payment/paystation/callback?status=Successful&invoice_number=LV1389-1752255805-1752783970-74&trx_id=TEST123"
```

## 🚨 **Common Problems & Quick Fixes**

### Problem: "Class not found" errors
**Fix:** Ensure both Payment and PayStation plugins are activated

### Problem: Routes not found
**Fix:** Clear caches:
```bash
php artisan route:clear
php artisan config:clear
php artisan optimize:clear
```

### Problem: PayStation API working but no orders
**Fix:** Check if `do_action(PAYMENT_ACTION_PAYMENT_PROCESSED)` is defined:
```php
if (function_exists('do_action') && defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
    echo "Payment processing available";
} else {
    echo "Payment processing not available";
}
```

## 📞 **PayStation Dashboard Configuration**

### Required URLs to configure:
1. **Callback URL:** `https://yourdomain.com/payment/paystation/callback`
2. **Webhook URL:** `https://yourdomain.com/payment/paystation/webhook`
3. **Success URL:** `https://yourdomain.com/checkout/success`
4. **Fail URL:** `https://yourdomain.com/checkout/failure`

### PayStation Settings to Verify:
- ✅ Merchant ID and Password correct
- ✅ Callback URLs saved and active
- ✅ IP whitelist includes your server (if required)
- ✅ Test mode vs Live mode settings

## 🔄 **Expected Flow (What Should Happen)**

1. **Customer completes payment on PayStation**
2. **PayStation sends callback:** `POST /payment/paystation/callback`
3. **Your server logs:** `PayStation: Callback received`
4. **Order lookup:** `PayStation: Starting order lookup`
5. **Payment verification:** `PayStation: Transaction status check result`
6. **Order processing:** `PayStation: Triggering payment processed action`
7. **Completion:** `PayStation: Payment processed action completed`
8. **Redirect:** Customer sees order confirmation

## 🆘 **If Nothing Works**

1. **Contact PayStation Support:**
   - Ask for callback configuration help
   - Request IP addresses to whitelist
   - Verify your callback URL is reachable from their servers

2. **Check Server Logs:**
   - Apache/Nginx error logs
   - PHP error logs
   - Firewall logs

3. **Test with Different Callback URL:**
   - Use the test-callback.php script temporarily
   - Verify PayStation can reach your server

## 📝 **Next Steps**

1. Run `php debug-live-callback.php`
2. Configure callback URL in PayStation dashboard
3. Make a test payment while monitoring logs
4. Check if callbacks are being received
5. If callbacks received, check order processing logic
6. If no callbacks, contact PayStation support

The enhanced logging will now show exactly where the process is failing!
