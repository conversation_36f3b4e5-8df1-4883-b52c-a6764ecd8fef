<?php

echo "PayStation Callback Test for bulbulee.com\n";
echo "=========================================\n\n";

// Test callback URL accessibility
echo "1. Testing Callback URL Accessibility:\n";

$callbackUrl = 'https://bulbulee.com/payment/paystation/callback';
echo "   Testing: $callbackUrl\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $callbackUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_USERAGENT, 'PayStation-Test-Agent');
curl_setopt($ch, CURLOPT_HEADER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "   ✗ Error: $error\n";
} else {
    echo "   ✓ Response Code: HTTP $httpCode\n";
    if ($httpCode == 400) {
        echo "   ✓ Route exists (400 is expected for GET request)\n";
    } elseif ($httpCode == 200) {
        echo "   ✓ Route accessible\n";
    } elseif ($httpCode == 404) {
        echo "   ✗ Route not found - check if PayStation plugin is activated\n";
    } else {
        echo "   ⚠ Unexpected response code\n";
    }
}

echo "\n2. Testing SSL Certificate:\n";

$sslInfo = curl_init();
curl_setopt($sslInfo, CURLOPT_URL, 'https://bulbulee.com');
curl_setopt($sslInfo, CURLOPT_RETURNTRANSFER, true);
curl_setopt($sslInfo, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($sslInfo, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($sslInfo, CURLOPT_TIMEOUT, 10);
curl_setopt($sslInfo, CURLOPT_NOBODY, true);

$sslResult = curl_exec($sslInfo);
$sslError = curl_error($sslInfo);
$sslCode = curl_getinfo($sslInfo, CURLINFO_HTTP_CODE);
curl_close($sslInfo);

if ($sslError) {
    echo "   ✗ SSL Error: $sslError\n";
    echo "   PayStation requires valid SSL certificate\n";
} else {
    echo "   ✓ SSL Certificate valid (HTTP $sslCode)\n";
}

echo "\n3. Testing Manual Callback:\n";

$testUrl = $callbackUrl . '?status=Successful&invoice_number=TEST-' . time() . '&trx_id=TEST123';
echo "   Sending test callback: $testUrl\n";

$testCh = curl_init();
curl_setopt($testCh, CURLOPT_URL, $testUrl);
curl_setopt($testCh, CURLOPT_POST, true);
curl_setopt($testCh, CURLOPT_RETURNTRANSFER, true);
curl_setopt($testCh, CURLOPT_TIMEOUT, 10);
curl_setopt($testCh, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($testCh, CURLOPT_USERAGENT, 'PayStation-Callback-Test');

$testResponse = curl_exec($testCh);
$testCode = curl_getinfo($testCh, CURLINFO_HTTP_CODE);
$testError = curl_error($testCh);
curl_close($testCh);

if ($testError) {
    echo "   ✗ Test callback failed: $testError\n";
} else {
    echo "   ✓ Test callback response: HTTP $testCode\n";
    echo "   Response: " . substr($testResponse, 0, 100) . "\n";
}

echo "\n4. PayStation Dashboard Configuration:\n";
echo "   Configure these URLs in your PayStation merchant dashboard:\n";
echo "   \n";
echo "   📍 Callback URL: https://bulbulee.com/payment/paystation/callback\n";
echo "   📍 Webhook URL: https://bulbulee.com/payment/paystation/webhook\n";
echo "   📍 Success URL: https://bulbulee.com/checkout/success\n";
echo "   📍 Fail URL: https://bulbulee.com/checkout/failure\n";

echo "\n5. Next Steps:\n";
echo "   1. ✅ Configure the URLs above in PayStation dashboard\n";
echo "   2. ✅ Make a test payment with small amount (10 BDT)\n";
echo "   3. ✅ Monitor logs: tail -f storage/logs/laravel-" . date('Y-m-d') . ".log\n";
echo "   4. ✅ Look for 'PayStation: Callback received' in logs\n";
echo "   5. ✅ Check if order appears in admin panel\n";

echo "\n6. Debugging Commands:\n";
echo "   # Monitor PayStation activity:\n";
echo "   tail -f storage/logs/laravel-" . date('Y-m-d') . ".log | grep -i paystation\n";
echo "   \n";
echo "   # Check callback reception:\n";
echo "   php debug-live-callback.php\n";
echo "   \n";
echo "   # Test with temporary callback:\n";
echo "   # Configure https://bulbulee.com/test-callback.php in PayStation dashboard\n";
echo "   # Then check: cat storage/logs/paystation-callback-test.log\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "SUMMARY FOR BULBULEE.COM:\n";
echo "========================\n";

if ($httpCode == 400 || $httpCode == 200) {
    echo "✅ Callback URL is accessible\n";
} else {
    echo "❌ Callback URL has issues\n";
}

if (!$sslError) {
    echo "✅ SSL certificate is valid\n";
} else {
    echo "❌ SSL certificate has issues\n";
}

echo "\n🎯 IMMEDIATE ACTION:\n";
echo "Go to PayStation dashboard and configure:\n";
echo "https://bulbulee.com/payment/paystation/callback\n";
echo "\nThen make a test payment and monitor the logs!\n";

echo "\nTest completed for bulbulee.com!\n";
