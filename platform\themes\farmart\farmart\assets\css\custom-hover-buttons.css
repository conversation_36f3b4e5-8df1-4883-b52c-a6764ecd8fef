/* Custom CSS to position product hover buttons correctly */
.product-inner .product-thumbnail .product-loop__buttons {
    right: -11px !important; /* Position buttons 11px to the right of the product card (moved 4px more to the left) */
    position: absolute !important;
    top: 0 !important;
    opacity: 0 !important;
    display: block !important;
    z-index: 10 !important;
}

/* Ensure the hover functionality still works */
.product-inner:hover .product-thumbnail .product-loop__buttons {
    opacity: 1 !important;
}

/* Adjust the transform for the hover effect */
.product-inner .product-thumbnail .product-loop__buttons .product-loop_button a {
    transform: translateX(10px) !important; /* Smaller transform distance for inside positioning */
    transition: 0.3s !important;
}

.product-inner:hover .product-thumbnail .product-loop__buttons .product-loop_button a {
    transform: translateX(0) !important;
}

/* Make sure the buttons are still visible and clickable */
.product-inner .product-thumbnail .product-loop__buttons .product-loop_button {
    margin-bottom: 10px !important;
}

/* Ensure the buttons are positioned in the white space */
.product-inner {
    overflow: visible !important;
}

/* Fix for RTL layouts if needed */
html[dir="rtl"] .product-inner .product-thumbnail .product-loop__buttons {
    right: auto !important;
    left: -11px !important;
}

html[dir="rtl"] .product-inner .product-thumbnail .product-loop__buttons .product-loop_button a {
    transform: translateX(-10px) !important;
}

html[dir="rtl"] .product-inner:hover .product-thumbnail .product-loop__buttons .product-loop_button a {
    transform: translateX(0) !important;
}
