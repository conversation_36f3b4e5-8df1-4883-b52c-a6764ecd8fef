@php
    // Count total images
    $totalImages = count($productImages);
@endphp

<style>
    /* Aggressive fix for image stacking issue in quick view */
    .bb-quick-view-gallery-images > a:not(:first-child) {
        display: none !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    .bb-quick-view-gallery-images > a:first-child {
        display: block !important;
        position: relative !important;
        left: auto !important;
        top: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Show all images only after slick is fully initialized */
    .bb-quick-view-gallery-images.slick-initialized > a,
    .bb-quick-view-gallery-images.slick-initialized .slick-slide {
        display: block !important;
        position: relative !important;
        left: auto !important;
        top: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
</style>

<div class="bb-quick-view-gallery-images position-relative">
    @foreach ($productImages as $image)
        <a href="{{ RvMedia::getImageUrl($image) }}">
            {{ RvMedia::image($image, $product->name, 'medium') }}
        </a>
    @endforeach

    <!-- Image Counter -->
    <div class="image-counter">
        <span class="current-image">1</span>/<span class="total-images">{{ $totalImages }}</span>
    </div>

    <!-- Navigation Arrows -->
    <div class="gallery-nav gallery-nav-prev">
        <button class="btn-gallery-nav btn-prev">
            <span class="svg-icon">
                <svg>
                    <use href="#svg-icon-chevron-left" xlink:href="#svg-icon-chevron-left"></use>
                </svg>
            </span>
        </button>
    </div>
    <div class="gallery-nav gallery-nav-next">
        <button class="btn-gallery-nav btn-next">
            <span class="svg-icon">
                <svg>
                    <use href="#svg-icon-chevron-right" xlink:href="#svg-icon-chevron-right"></use>
                </svg>
            </span>
        </button>
    </div>
</div>

<style>
    /* Image Counter Styles */
    .image-counter {
        position: absolute !important;
        bottom: 15px !important;
        right: 15px !important;
        background-color: rgba(0, 0, 0, 0.6) !important;
        color: #fff !important;
        padding: 5px 10px !important;
        border-radius: 15px !important;
        font-size: 12px !important;
        z-index: 999 !important;
        display: block !important;
    }

    /* Navigation Arrows Styles */
    .gallery-nav {
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 999 !important;
        display: block !important;
    }

    .gallery-nav-prev {
        left: 10px !important;
    }

    .gallery-nav-next {
        right: 10px !important;
    }

    .btn-gallery-nav {
        background-color: rgba(255, 255, 255, 0.7) !important;
        border: none !important;
        border-radius: 50% !important;
        width: 40px !important;
        height: 40px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .btn-gallery-nav:hover {
        background-color: rgba(255, 255, 255, 0.9) !important;
    }

    .btn-gallery-nav .svg-icon {
        width: 20px !important;
        height: 20px !important;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure the elements are visible
        $('.image-counter, .gallery-nav').css('display', 'block');

        // Initialize gallery immediately if EcommerceApp is available
        if (typeof EcommerceApp !== 'undefined') {
            EcommerceApp.initProductGallery(true);
        }

        // Wait for slick to be initialized
        setTimeout(function() {
            // Update the current image number when the slide changes
            $('.bb-quick-view-gallery-images').on('afterChange', function(event, slick, currentSlide) {
                $('.current-image').text(currentSlide + 1);
            });

            // Add click handlers for the navigation buttons
            $('.btn-prev').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-quick-view-gallery-images').slick('slickPrev');
                return false;
            });

            $('.btn-next').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-quick-view-gallery-images').slick('slickNext');
                return false;
            });
        }, 500); // Reduced wait time from 1000ms to 500ms
    });
</script>
