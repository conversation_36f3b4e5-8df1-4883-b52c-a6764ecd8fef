# PayStation Payment Gateway Plugin

This plugin integrates PayStation payment gateway with your Laravel e-commerce application, providing the same functionality as the WordPress PayStation plugin.

## Features

- **Payment Processing**: Accept payments through PayStation in Bangladesh Taka (BDT)
- **Multiple Payment Methods**: Support for Credit/Debit cards, Internet banking, and Mobile banking
- **EMI Support**: Optional EMI (Equated Monthly Installment) with minimum 5000 BDT
- **Charge Options**: Configurable charge settings (with or without charge)
- **Transaction Verification**: Real-time transaction status verification
- **Callback Handling**: Secure payment callback processing
- **Admin Configuration**: Easy setup through admin panel

## Installation

1. The plugin is already created in `platform/plugins/paystation/`
2. Activate the plugin through your application's plugin management system
3. Configure the payment method in Admin Panel > Plugins > Payment

## Configuration

### Required Settings

1. **Merchant ID**: Your PayStation merchant ID
2. **Password**: Your PayStation API password
3. **Charge Setting**: Choose whether to include charges
4. **EMI Option**: Enable/disable EMI (minimum 5000 BDT required)
5. **Fail URL**: URL to redirect on payment failure/cancellation

### PayStation Account Setup

1. Register an account at [PayStation](https://www.paystation.com.bd)
2. Complete the verification process
3. Obtain your Merchant ID and Password from PayStation dashboard
4. Configure webhook URLs in PayStation dashboard:
   - Callback URL: `{your-domain}/payment/paystation/callback`
   - Webhook URL: `{your-domain}/payment/paystation/webhook`

## API Endpoints

- **Payment Initiation**: `https://api.paystation.com.bd/initiate-payment`
- **Transaction Status**: `https://api.paystation.com.bd/transaction-status`

## Callback URLs

- **Success/Failure Callback**: `/payment/paystation/callback`
- **Webhook**: `/payment/paystation/webhook`

## Payment Flow

1. Customer selects PayStation as payment method
2. Order details are sent to PayStation API
3. Customer is redirected to PayStation payment page
4. After payment, customer is redirected back to your site
5. Payment status is verified with PayStation API
6. Order status is updated accordingly

## Supported Currency

- **BDT** (Bangladesh Taka) - Primary and only supported currency

## EMI Feature

- Minimum amount: 5000 BDT
- Automatically validates amount before allowing EMI option
- Configurable through admin panel

## Error Handling

- API connection failures are logged and handled gracefully
- Invalid callback requests are rejected
- Transaction verification ensures payment authenticity
- Failed payments are properly recorded and handled

## Security Features

- Transaction status verification through PayStation API
- Secure callback URL handling
- Input validation and sanitization
- Error logging for debugging

## Testing

1. Use PayStation's sandbox/test environment for initial testing
2. Test with small amounts before going live
3. Verify callback URLs are accessible
4. Test both successful and failed payment scenarios

## Troubleshooting

### Common Issues

1. **"Payment method not configured"**: Check Merchant ID and Password
2. **"EMI minimum amount error"**: Ensure amount is at least 5000 BDT for EMI
3. **Callback not working**: Verify callback URLs are publicly accessible
4. **Currency not supported"**: PayStation only supports BDT currency

### Logs

Check application logs for PayStation-related errors:
- Payment initiation errors
- Callback processing issues
- API communication problems

## Support

For PayStation-specific issues, contact PayStation support at [https://www.paystation.com.bd](https://www.paystation.com.bd)

For plugin-related issues, check the application logs and ensure all configuration settings are correct.

## Version History

- **1.0.0**: Initial release with full PayStation integration
  - Payment processing
  - EMI support
  - Callback handling
  - Admin configuration
  - Transaction verification
