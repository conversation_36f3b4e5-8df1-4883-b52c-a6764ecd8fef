@php
    EcommerceHelper::registerThemeAssets();
    $version = get_cms_version();
    Theme::asset()->add('lightgallery-css', 'vendor/core/plugins/ecommerce/libraries/lightgallery/css/lightgallery.min.css', version: $version);
    Theme::asset()->container('footer')->add('lightgallery-js', 'vendor/core/plugins/ecommerce/libraries/lightgallery/js/lightgallery.min.js', ['jquery'], version: $version);

    $galleryStyle = theme_option('ecommerce_product_gallery_image_style', 'vertical');
    $totalImages = count($productImages);
    $totalVideos = 0;
    if (!empty($product->video)) {
        foreach($product->video as $video) {
            if ($video['url']) {
                $totalVideos++;
            }
        }
    }
    $totalMedia = $totalImages + $totalVideos;
@endphp

<style>
    /* Custom Image Gallery - No Slick, No Stacking */
    .custom-product-gallery {
        position: relative;
        width: 100%;
        overflow: visible;
    }

    .custom-gallery-main {
        position: relative;
        width: 100%;
        height: auto;
        min-height: 300px;
        overflow: visible;
        background: transparent;
        border-radius: 0;
        cursor: grab;
        user-select: none;
    }

    .custom-gallery-main:active {
        cursor: grabbing;
    }

    .custom-gallery-item {
        display: none;
        position: relative;
        width: 100%;
        text-align: center;
        line-height: 0;
    }

    .custom-gallery-item.active {
        display: block;
    }

    .custom-gallery-item img {
        width: 100%;
        height: auto;
        max-width: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        cursor: zoom-in;
    }

    /* Amazon-style zoom effect for desktop */
    @media (min-width: 992px) {
        .custom-gallery-item {
            border-radius: 8px;
            position: relative;
        }

        .custom-gallery-item img {
            height: 450px;
            object-fit: contain;
            background: transparent;
            cursor: crosshair;
        }

        .custom-gallery-main {
            min-height: 450px;
            max-height: 450px;
            border-radius: 0;
            box-shadow: none;
            position: relative;
        }

        /* Zoom lens overlay */
        .zoom-lens {
            position: absolute;
            border: 2px solid #007bff;
            background: rgba(255,255,255,0.3);
            width: 150px;
            height: 150px;
            display: none;
            pointer-events: none;
            z-index: 15;
            border-radius: 4px;
        }

        /* Zoomed image container */
        .zoom-result {
            position: absolute;
            left: calc(100% + 15px);
            top: 0;
            width: 400px;
            height: 400px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            display: none;
            z-index: 99999;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            pointer-events: none;
        }

        /* Adjust zoom result position if not enough space on right */
        @media (max-width: 1200px) {
            .zoom-result {
                left: calc(-270px);
            }
        }



        .zoom-result img {
            position: absolute;
            left: 0;
            top: 0;
            width: auto;
            height: auto;
            max-width: none;
            max-height: none;
        }


    }

    /* Mobile adjustments */
    @media (max-width: 991px) {
        .custom-gallery-main {
            min-height: 300px;
        }

        .custom-gallery-item img {
            height: 400px;
            object-fit: contain;
            background: transparent;
        }
    }
    
    .custom-gallery-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.15);
        color: #333;
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
    }

    .custom-gallery-main:hover .custom-gallery-nav {
        opacity: 1;
        visibility: hidden;
    }

    .custom-gallery-nav:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-50%) scale(1.1);
        visibility: hidden;
    }

    .custom-gallery-prev {
        left: 0px;
    }

    .custom-gallery-next {
        right: 0px;
    }

    /* Show navigation on mobile always */
    @media (max-width: 991px) {
        .custom-gallery-nav {
            opacity: 1;
            visibility: hidden;
            background-color: rgba(255, 255, 255, 0.15);
            border: none;
            color: #333;
            width: 50px;
            height: 50px;
            font-size: 28px;
            font-weight: bold;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .custom-gallery-nav:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-50%) scale(1.1);
        }

        .custom-gallery-nav:active {
            transform: translateY(-50%) scale(0.95);
        }

        .custom-gallery-prev {
            left: 15px;
        }

        .custom-gallery-next {
            right: 15px;
        }
    }

    .custom-gallery-counter {
        position: absolute;
        bottom: 20px;
        right: 20px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        z-index: 10;
        font-weight: 500;
    }



    .custom-gallery-thumbnails {
        display: flex;
        gap: 8px;
        margin-top: 5px;
        overflow-x: auto;
        padding: 10px 5px;
        scrollbar-width: 0px;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar {
        height: 2px;
        visibility: hidden;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
        visibility: hidden;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
        visibility: hidden;
    }

    .custom-gallery-thumb {
        flex-shrink: 0;
        width: 70px;
        height: 70px;
        border: 2px solid transparent;
        border-radius: 6px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        background: transparent;
    }

    .custom-gallery-thumb.active {
        border-color: #ff6633;
        box-shadow: 0 0 0 1px rgba(187, 112, 26, 0.35);
    }

    .custom-gallery-thumb:hover {
        border-color: #ff6633;
        transform: scale(1.05);
    }

    .custom-gallery-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s ease;
    }

    .custom-gallery-thumb:hover img {
        transform: scale(1.1);
    }

    /* Desktop thumbnail adjustments */
    @media (min-width: 992px) {
        .custom-gallery-thumbnails {
            gap: 12px;
            margin-top: 15px;
        }

        .custom-gallery-thumb {
            width: 90px;
            height: 90px;
        }
    }
    
    .custom-video-item {
        position: relative;
    }
    
    .custom-video-play {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.7);
        color: white;
        border: none;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        font-size: 24px;
        cursor: pointer;
        z-index: 5;
    }
</style>

<div class="custom-product-gallery">
    <div class="custom-gallery-main">
        @php $itemIndex = 0; @endphp
        
        {{-- Videos --}}
        @if (!empty($product->video))
            @foreach($product->video as $video)
                @continue(!$video['url'])
                <div class="custom-gallery-item custom-video-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                    @if ($video['provider'] === 'video')
                        <video controls style="max-width: 100%; max-height: 100%;">
                            <source src="{{ $video['url'] }}" type="video/mp4">
                        </video>
                    @else
                        <iframe 
                            src="{{ $video['url'] }}" 
                            style="width: 100%; height: 100%; border: none;"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    @endif
                </div>
                @php $itemIndex++; @endphp
            @endforeach
        @endif
        
        {{-- Images --}}
        @foreach ($productImages as $image)
            <div class="custom-gallery-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                <a href="{{ RvMedia::getImageUrl($image) }}">
                    {{ RvMedia::image($image, $product->name) }}
                </a>
                <div class="zoom-lens"></div>
            </div>
            @php $itemIndex++; @endphp
        @endforeach
        
        {{-- Navigation --}}
        @if ($totalMedia > 1)
            <button class="custom-gallery-nav custom-gallery-prev" onclick="customGallery.prev()">‹</button>
            <button class="custom-gallery-nav custom-gallery-next" onclick="customGallery.next()">›</button>
        @endif

        {{-- Counter --}}
        <div class="custom-gallery-counter">
            <span class="current-image">1</span>/<span class="total-images">{{ $totalMedia }}</span>
        </div>
    </div>

    {{-- Single zoom window for all images --}}
    <div class="zoom-result">
        <img src="" alt="{{ $product->name }}">
    </div>

    {{-- Thumbnails --}}
    @if ($totalMedia > 1)
        <div class="custom-gallery-thumbnails">
            @php $thumbIndex = 0; @endphp
            
            {{-- Video thumbnails --}}
            @if (!empty($product->video))
                @foreach($product->video as $video)
                    @continue(!$video['url'])
                    <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                        <img src="{{ $video['thumbnail'] ?? asset('images/video-placeholder.jpg') }}" alt="{{ $product->name }}">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 20px;">▶</div>
                    </div>
                    @php $thumbIndex++; @endphp
                @endforeach
            @endif
            
            {{-- Image thumbnails --}}
            @foreach ($productImages as $image)
                <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                    {{ RvMedia::image($image, $product->name, 'thumb') }}
                </div>
                @php $thumbIndex++; @endphp
            @endforeach
        </div>
    @endif
</div>

<script>
window.customGallery = {
    currentIndex: 0,
    totalItems: {{ $totalMedia }},
    touchStartX: 0,
    touchEndX: 0,

    init: function() {
        this.updateDisplay();
        this.initLightGallery();
        this.initZoomEffect();
        this.initTouchEvents();
    },
    
    goTo: function(index) {
        if (index >= 0 && index < this.totalItems) {
            this.currentIndex = index;
            this.updateDisplay();
            // Reinitialize zoom for new active image
            setTimeout(() => this.initZoomEffect(), 100);
        }
    },
    
    next: function() {
        this.goTo((this.currentIndex + 1) % this.totalItems);
    },
    
    prev: function() {
        this.goTo((this.currentIndex - 1 + this.totalItems) % this.totalItems);
    },
    
    updateDisplay: function() {
        // Update main gallery
        document.querySelectorAll('.custom-gallery-item').forEach((item, index) => {
            item.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update thumbnails
        document.querySelectorAll('.custom-gallery-thumb').forEach((thumb, index) => {
            thumb.classList.toggle('active', index === this.currentIndex);
        });

        // Update counter
        document.querySelector('.current-image').textContent = this.currentIndex + 1;

    },
    
    initLightGallery: function() {
        if (typeof $.fn.lightGallery !== 'undefined') {
            $('.custom-gallery-main').lightGallery({
                selector: '.custom-gallery-item a',
                thumbnail: true,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
        }
    },

    initZoomEffect: function() {
        // Only enable zoom on desktop
        if (window.innerWidth < 992) return;

        const galleryItems = document.querySelectorAll('.custom-gallery-item');
        const result = document.querySelector('.zoom-result');
        const resultImg = result ? result.querySelector('img') : null;

        if (!result || !resultImg) return;

        galleryItems.forEach(item => {
            const img = item.querySelector('a img');
            const lens = item.querySelector('.zoom-lens');

            if (!img || !lens) return;

            let cx, cy;

            // Wait for image to load before calculating zoom
            function initializeZoom() {
                if (img.complete && img.naturalWidth > 0) {
                    calculateZoom();
                } else {
                    img.onload = calculateZoom;
                }
            }

            // Calculate zoom ratio
            function calculateZoom() {
                const imgRect = img.getBoundingClientRect();
                const zoomFactor = 2.5; // Increase zoom level

                console.log('Calculating zoom - img natural size:', img.naturalWidth, 'x', img.naturalHeight);
                console.log('Result container size:', result.offsetWidth, 'x', result.offsetHeight);

                // Calculate zoom ratios based on natural image size
                cx = (img.naturalWidth * zoomFactor) / result.offsetWidth;
                cy = (img.naturalHeight * zoomFactor) / result.offsetHeight;

                // Set result image size to be larger than the result container
                const newWidth = img.naturalWidth * zoomFactor;
                const newHeight = img.naturalHeight * zoomFactor;

                resultImg.style.width = newWidth + 'px';
                resultImg.style.height = newHeight + 'px';

                console.log('Result image size set to:', newWidth, 'x', newHeight);
            }

            // Mouse move handler
            function moveLens(e) {
                e.preventDefault();
                const pos = getCursorPos(e);
                const imgRect = img.getBoundingClientRect();
                let x = pos.x - (lens.offsetWidth / 2);
                let y = pos.y - (lens.offsetHeight / 2);

                // Prevent lens from going outside image
                if (x > imgRect.width - lens.offsetWidth) x = imgRect.width - lens.offsetWidth;
                if (x < 0) x = 0;
                if (y > imgRect.height - lens.offsetHeight) y = imgRect.height - lens.offsetHeight;
                if (y < 0) y = 0;

                // Set lens position
                lens.style.left = x + 'px';
                lens.style.top = y + 'px';

                // Calculate the position in the natural image coordinates
                const scaleX = img.naturalWidth / imgRect.width;
                const scaleY = img.naturalHeight / imgRect.height;

                const naturalX = (x + lens.offsetWidth / 2) * scaleX;
                const naturalY = (y + lens.offsetHeight / 2) * scaleY;

                // Calculate zoom position to center the lens area in result
                const zoomFactor = 2.5;
                const zoomX = naturalX * zoomFactor - result.offsetWidth / 2;
                const zoomY = naturalY * zoomFactor - result.offsetHeight / 2;

                // Display corresponding part in result
                resultImg.style.left = '-' + zoomX + 'px';
                resultImg.style.top = '-' + zoomY + 'px';
            }

            // Get cursor position relative to image
            function getCursorPos(e) {
                const rect = img.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                return { x: x, y: y };
            }

            // Mouse enter
            img.addEventListener('mouseenter', function() {
                if (item.classList.contains('active')) {
                    console.log('Zoom activated for:', img.src);
                    // Set the zoom result image source to current image
                    resultImg.src = img.src;
                    console.log('Result image src set to:', resultImg.src);

                    // Wait for the result image to load before initializing zoom
                    if (resultImg.complete) {
                        initializeZoom();
                    } else {
                        resultImg.onload = initializeZoom;
                    }

                    lens.style.display = 'block';
                    result.style.display = 'block';
                    console.log('Zoom result display set to block, dimensions:', result.offsetWidth, 'x', result.offsetHeight);
                }
            });

            // Mouse leave
            img.addEventListener('mouseleave', function() {
                lens.style.display = 'none';
                result.style.display = 'none';
            });

            // Mouse move
            img.addEventListener('mousemove', moveLens);
            lens.addEventListener('mousemove', moveLens);
        });
    },

    initTouchEvents: function() {
        const galleryMain = document.querySelector('.custom-gallery-main');
        if (!galleryMain) return;

        // Touch events for swipe navigation
        galleryMain.addEventListener('touchstart', (e) => {
            this.touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });

        galleryMain.addEventListener('touchend', (e) => {
            this.touchEndX = e.changedTouches[0].screenX;
            this.handleSwipe();
        }, { passive: true });

        // Mouse events for drag navigation (desktop)
        let isDragging = false;
        let startX = 0;

        galleryMain.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            galleryMain.style.cursor = 'grabbing';
        });

        galleryMain.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
        });

        galleryMain.addEventListener('mouseup', (e) => {
            if (!isDragging) return;
            isDragging = false;
            galleryMain.style.cursor = 'grab';

            const endX = e.clientX;
            const diffX = startX - endX;

            if (Math.abs(diffX) > 50) { // Minimum swipe distance
                if (diffX > 0) {
                    this.next();
                } else {
                    this.prev();
                }
            }
        });

        galleryMain.addEventListener('mouseleave', () => {
            isDragging = false;
            galleryMain.style.cursor = 'grab';
        });
    },

    handleSwipe: function() {
        const swipeThreshold = 50;
        const diffX = this.touchStartX - this.touchEndX;

        if (Math.abs(diffX) > swipeThreshold) {
            if (diffX > 0) {
                this.next(); // Swipe left - next image
            } else {
                this.prev(); // Swipe right - previous image
            }
        }
    },

    // Function to update gallery when product options change
    updateGalleryForVariation: function(variationImages) {
        console.log('updateGalleryForVariation called with:', variationImages);

        const galleryItems = document.querySelectorAll('.custom-gallery-item');
        const thumbnails = document.querySelectorAll('.custom-gallery-thumb');

        // If no variation images, show all original images
        if (!variationImages || variationImages.length === 0) {
            galleryItems.forEach(item => item.style.display = 'block');
            thumbnails.forEach(thumb => thumb.style.display = 'block');
            this.currentIndex = 0;
            this.updateDisplay();
            return;
        }

        // Hide all current items first
        galleryItems.forEach(item => item.style.display = 'none');
        thumbnails.forEach(thumb => thumb.style.display = 'none');

        // Show variation images
        variationImages.forEach((imageUrl, index) => {
            if (galleryItems[index]) {
                const img = galleryItems[index].querySelector('img');
                const link = galleryItems[index].querySelector('a');

                if (img && link && imageUrl) {
                    // Update the image source and link
                    img.src = imageUrl;
                    img.alt = img.alt || 'Product variation image';
                    link.href = imageUrl;

                    // Show this gallery item
                    galleryItems[index].style.display = 'block';

                    // Update corresponding thumbnail
                    if (thumbnails[index]) {
                        const thumbImg = thumbnails[index].querySelector('img');
                        if (thumbImg) {
                            thumbImg.src = imageUrl;
                        }
                        thumbnails[index].style.display = 'block';
                    }
                }
            }
        });

        // Update total items and reset to first image
        this.totalItems = variationImages.length;
        this.currentIndex = 0;
        this.updateDisplay();

        // Update counter
        const totalImagesElement = document.querySelector('.total-images');
        if (totalImagesElement) {
            totalImagesElement.textContent = this.totalItems;
        }

        // Reinitialize zoom for the new image
        setTimeout(() => this.initZoomEffect(), 100);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    customGallery.init();
    customGallery.initZoomEffect();
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft') {
        customGallery.prev();
    } else if (e.key === 'ArrowRight') {
        customGallery.next();
    }
});
</script>
