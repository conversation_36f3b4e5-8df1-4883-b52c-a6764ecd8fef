(()=>{var s;(s=jQuery).fn.expireCountdown=function(){return this.each((function(){var n=s(this),e=n.data("expire"),t=function(s){if(s){var e={days:Math.floor(s/86400),hours:Math.floor(s%86400/3600),minutes:Math.floor(s%3600/60),seconds:Math.floor(s%60)},t=window.siteConfig.countdown_text||{days:"days",hours:"hours",minutes:"mins",seconds:"secs"};Object.keys(e).forEach((function(s){if("days"!=s||0!=e[s]){var a=n.find("."+s+" .digits");a.length?parseInt(a.text())!=e[s]&&a.text(e[s]>9?e[s]:"0"+e[s]):n.append('\n        <span class="__class__ timer">\n            <span class="digits">__digits__</span>\n            <span class="text">__text__</span>\n        </span>\n        <span class="divider">:</span>'.replace("__class__",s).replace("__digits__",e[s]>9?e[s]:"0"+e[s]).replace("__text__",t[s]))}})),n.closest(".countdown-wrapper").removeClass("d-none")}};t(e);var a=setInterval((function(){t(e-=1),e<0&&clearInterval(a)}),1e3)}))},s((function(){s(".expire-countdown").expireCountdown()}))})();
