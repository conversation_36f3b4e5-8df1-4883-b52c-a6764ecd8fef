<?php

echo "PayStation API Test (Official Documentation)\n";
echo "===========================================\n\n";

// Test configuration - REPLACE WITH YOUR ACTUAL CREDENTIALS
$merchantId = 'YOUR_MERCHANT_ID'; // e.g., '204-16537301811'
$password = 'YOUR_PASSWORD';      // e.g., 'gamepass'

if ($merchantId === 'YOUR_MERCHANT_ID' || $password === 'YOUR_PASSWORD') {
    echo "⚠️  WARNING: Please update the test credentials in this script!\n";
    echo "Set your actual PayStation Merchant ID and Password\n\n";
}

// Test 1: Payment Initiation API
echo "1. Testing Payment Initiation API\n";
echo "==================================\n";

$invoiceNumber = 'TEST-' . time(); // Unique invoice number
$testData = [
    'merchantId' => $merchantId,
    'password' => $password,
    'invoice_number' => $invoiceNumber,
    'currency' => 'BDT',
    'payment_amount' => '10', // Test with 10 BDT
    'pay_with_charge' => '1',
    'reference' => 'API Test',
    'cust_name' => 'Test Customer',
    'cust_phone' => '01700000000',
    'cust_email' => '<EMAIL>',
    'cust_address' => 'Test Address',
    'callback_url' => 'https://yourdomain.com/payment/paystation/callback',
    'checkout_items' => 'Test Items',
    'emi' => '0'
];

echo "Request Data:\n";
foreach ($testData as $key => $value) {
    if ($key === 'password') {
        echo "  $key: " . str_repeat('*', strlen($value)) . "\n";
    } else {
        echo "  $key: $value\n";
    }
}
echo "\n";

// Make the API request
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => 'https://api.paystation.com.bd/initiate-payment',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => $testData,
    CURLOPT_SSL_VERIFYPEER => false
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$error = curl_error($curl);
curl_close($curl);

echo "Response (HTTP $httpCode):\n";
if ($error) {
    echo "❌ CURL Error: $error\n";
} else {
    $result = json_decode($response, true);
    if ($result) {
        echo "✅ JSON Response:\n";
        echo json_encode($result, JSON_PRETTY_PRINT) . "\n";
        
        // Check response format according to documentation
        if (isset($result['status_code']) && $result['status_code'] === '200') {
            echo "✅ Success: Payment link created\n";
            if (isset($result['payment_url'])) {
                echo "💳 Payment URL: " . $result['payment_url'] . "\n";
            }
        } else {
            echo "❌ Failed: " . ($result['message'] ?? 'Unknown error') . "\n";
            echo "Status Code: " . ($result['status_code'] ?? 'Not provided') . "\n";
        }
    } else {
        echo "❌ Invalid JSON Response:\n";
        echo $response . "\n";
    }
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Transaction Status API
echo "2. Testing Transaction Status API\n";
echo "==================================\n";

$header = [
    'merchantId:' . $merchantId
];

$body = [
    'invoice_number' => $invoiceNumber
];

echo "Request Header:\n";
echo "  merchantId: $merchantId\n";
echo "Request Body:\n";
echo "  invoice_number: $invoiceNumber\n\n";

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => 'https://api.paystation.com.bd/transaction-status',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => $header,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => $body,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 30
]);

$statusResponse = curl_exec($curl);
$statusHttpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$statusError = curl_error($curl);
curl_close($curl);

echo "Response (HTTP $statusHttpCode):\n";
if ($statusError) {
    echo "❌ CURL Error: $statusError\n";
} else {
    $statusResult = json_decode($statusResponse, true);
    if ($statusResult) {
        echo "✅ JSON Response:\n";
        echo json_encode($statusResult, JSON_PRETTY_PRINT) . "\n";
        
        // Check response format according to documentation
        if (isset($statusResult['status_code']) && $statusResult['status_code'] === '200') {
            echo "✅ Success: Transaction found\n";
            if (isset($statusResult['data'])) {
                $data = $statusResult['data'];
                echo "Transaction Status: " . ($data['trx_status'] ?? 'Unknown') . "\n";
                echo "Transaction ID: " . ($data['trx_id'] ?? 'Not available') . "\n";
                echo "Payment Amount: " . ($data['payment_amount'] ?? 'Unknown') . "\n";
                echo "Payment Method: " . ($data['payment_method'] ?? 'Not available') . "\n";
            }
        } else {
            echo "❌ Failed: " . ($statusResult['message'] ?? 'Unknown error') . "\n";
            echo "Status Code: " . ($statusResult['status_code'] ?? 'Not provided') . "\n";
        }
    } else {
        echo "❌ Invalid JSON Response:\n";
        echo $statusResponse . "\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// Summary and next steps
echo "📋 Test Summary\n";
echo "===============\n";

if ($merchantId === 'YOUR_MERCHANT_ID') {
    echo "⚠️  Update credentials: Set your actual PayStation Merchant ID and Password\n";
} else {
    echo "✅ Credentials configured\n";
}

echo "\n📝 Next Steps:\n";
echo "1. Update the credentials in this script with your actual PayStation details\n";
echo "2. Run this script to test API connectivity\n";
echo "3. If APIs work, activate the PayStation plugin in admin panel\n";
echo "4. Configure the same credentials in the plugin settings\n";
echo "5. Test the full payment flow\n";

echo "\n🔗 Important URLs:\n";
echo "- PayStation Dashboard: https://www.paystation.com.bd\n";
echo "- Payment Initiation API: https://api.paystation.com.bd/initiate-payment\n";
echo "- Transaction Status API: https://api.paystation.com.bd/transaction-status\n";

echo "\n📞 Callback URL Format:\n";
echo "Your callback URL will receive parameters like:\n";
echo "?status=Successful&invoice_number=123456&trx_id=10XB9900\n";

echo "\n🔧 Troubleshooting:\n";
echo "- If you get authentication errors, verify your Merchant ID and Password\n";
echo "- If you get network errors, check if your server can make HTTPS requests\n";
echo "- If you get duplicate invoice errors, the invoice number must be unique\n";

echo "\nTest completed!\n";
