<?php

require_once 'vendor/autoload.php';

echo "PayStation Integration Test\n";
echo "===========================\n\n";

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "1. Testing Plugin Constants:\n";
$constantsFile = 'platform/plugins/paystation/helpers/constants.php';
if (file_exists($constantsFile)) {
    include_once $constantsFile;
    if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
        echo "   ✓ PAYSTATION_PAYMENT_METHOD_NAME: " . PAYSTATION_PAYMENT_METHOD_NAME . "\n";
    } else {
        echo "   ✗ PAYSTATION_PAYMENT_METHOD_NAME not defined\n";
    }
} else {
    echo "   ✗ Constants file not found\n";
}

echo "\n2. Testing Helper Functions:\n";
try {
    if (function_exists('get_payment_setting')) {
        echo "   ✓ get_payment_setting function exists\n";
        
        // Test getting PayStation settings
        $merchantId = get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME);
        $password = get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME);
        $status = get_payment_setting('status', PAYSTATION_PAYMENT_METHOD_NAME);
        
        echo "   - Merchant ID: " . ($merchantId ? 'SET' : 'NOT SET') . "\n";
        echo "   - Password: " . ($password ? 'SET' : 'NOT SET') . "\n";
        echo "   - Status: " . ($status ? 'ENABLED' : 'DISABLED') . "\n";
        
    } else {
        echo "   ✗ get_payment_setting function not found\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error testing helper functions: " . $e->getMessage() . "\n";
}

echo "\n3. Testing Route Registration:\n";
try {
    $router = app('router');
    $routes = $router->getRoutes();
    
    $paystationRoutes = [];
    foreach ($routes as $route) {
        if (strpos($route->getName() ?? '', 'paystation') !== false) {
            $paystationRoutes[] = $route->getName() . ' -> ' . $route->uri();
        }
    }
    
    if (!empty($paystationRoutes)) {
        echo "   ✓ PayStation routes found:\n";
        foreach ($paystationRoutes as $route) {
            echo "     - $route\n";
        }
    } else {
        echo "   ✗ No PayStation routes found\n";
        echo "     This might indicate the plugin is not activated\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking routes: " . $e->getMessage() . "\n";
}

echo "\n4. Testing Service Provider Registration:\n";
try {
    $providers = app()->getLoadedProviders();
    $paystationProvider = 'Botble\\PayStation\\Providers\\PayStationServiceProvider';
    
    if (isset($providers[$paystationProvider])) {
        echo "   ✓ PayStationServiceProvider is loaded\n";
    } else {
        echo "   ✗ PayStationServiceProvider is NOT loaded\n";
        echo "     The plugin may not be activated\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking service providers: " . $e->getMessage() . "\n";
}

echo "\n5. Testing Payment Method Registration:\n";
try {
    if (class_exists('Botble\\Payment\\Facades\\PaymentMethods')) {
        echo "   ✓ PaymentMethods facade exists\n";
        
        // This would require the plugin to be fully loaded
        // We'll just check if the class exists for now
    } else {
        echo "   ✗ PaymentMethods facade not found\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error testing payment methods: " . $e->getMessage() . "\n";
}

echo "\n6. Testing API Connectivity:\n";
try {
    // Test if we can reach PayStation API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.paystation.com.bd/initiate-payment');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode > 0) {
        echo "   ✓ PayStation API is reachable (HTTP $httpCode)\n";
    } else {
        echo "   ✗ Cannot reach PayStation API\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error testing API connectivity: " . $e->getMessage() . "\n";
}

echo "\n7. Common Issues & Solutions:\n";
echo "   Issue: Plugin not activated\n";
echo "   Solution: Go to Admin Panel > Plugins and activate PayStation\n\n";

echo "   Issue: Missing configuration\n";
echo "   Solution: Configure Merchant ID and Password in Admin Panel > Payment Settings\n\n";

echo "   Issue: Routes not working\n";
echo "   Solution: Clear caches with 'php artisan optimize:clear'\n\n";

echo "   Issue: Payment failed error\n";
echo "   Solution: Check logs in storage/logs/ for specific error messages\n\n";

echo "   Issue: Callback URL not accessible\n";
echo "   Solution: Ensure your server is publicly accessible for PayStation callbacks\n\n";

echo "Test completed!\n";
echo "\nIf you're still getting 'Payment failed!' errors:\n";
echo "1. Check the Laravel logs in storage/logs/\n";
echo "2. Ensure the plugin is activated in admin panel\n";
echo "3. Verify PayStation credentials are correct\n";
echo "4. Test with PayStation sandbox first\n";
echo "5. Check if your server can make outbound HTTPS requests\n";
