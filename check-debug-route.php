<?php

echo "PayStation Debug Route Check\n";
echo "============================\n\n";

// Load Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Checking PayStation debug route availability...\n\n";

try {
    // Check if the route exists
    $router = app('router');
    $routes = $router->getRoutes();
    
    $debugRouteFound = false;
    $callbackRouteFound = false;
    
    foreach ($routes as $route) {
        $routeName = $route->getName();
        $routeUri = $route->uri();
        
        if (strpos($routeName ?? '', 'paystation.debug') !== false) {
            $debugRouteFound = true;
            echo "✅ Debug route found: $routeUri (name: $routeName)\n";
        }
        
        if (strpos($routeName ?? '', 'paystation.callback') !== false) {
            $callbackRouteFound = true;
            echo "✅ Callback route found: $routeUri (name: $routeName)\n";
        }
    }
    
    if (!$debugRouteFound) {
        echo "❌ Debug route not found\n";
        echo "   This means the PayStation plugin routes are not loaded\n";
    }
    
    if (!$callbackRouteFound) {
        echo "❌ Callback route not found\n";
        echo "   This means the PayStation plugin is not activated\n";
    }
    
    echo "\n";
    
    // Check if PayStation plugin is activated
    $activatedPlugins = setting('activated_plugins', []);
    echo "Activated plugins: " . implode(', ', $activatedPlugins) . "\n";
    
    $paymentActive = in_array('payment', $activatedPlugins);
    $paystationActive = in_array('paystation', $activatedPlugins);
    
    echo "Payment plugin active: " . ($paymentActive ? 'YES' : 'NO') . "\n";
    echo "PayStation plugin active: " . ($paystationActive ? 'YES' : 'NO') . "\n\n";
    
    // Check if debug controller file exists
    $debugControllerPath = 'platform/plugins/paystation/src/Http/Controllers/PayStationDebugController.php';
    echo "Debug controller file exists: " . (file_exists($debugControllerPath) ? 'YES' : 'NO') . "\n";
    
    if (file_exists($debugControllerPath)) {
        echo "Debug controller path: $debugControllerPath\n";
    }
    
    echo "\n=== DIAGNOSIS ===\n";
    
    if (!$paystationActive) {
        echo "🚨 CRITICAL: PayStation plugin is not activated\n";
        echo "   → Activate PayStation plugin in admin panel\n";
        echo "   → This will load the debug routes\n\n";
    } elseif (!$debugRouteFound) {
        echo "⚠️ WARNING: PayStation plugin active but debug route not found\n";
        echo "   → Clear route cache: php artisan route:clear\n";
        echo "   → Check if routes/web.php is properly configured\n\n";
    } else {
        echo "✅ GOOD: Debug route is available\n";
        echo "   → Access it at: https://bulbulee.com/payment/paystation/debug\n\n";
    }
    
    if (!$paymentActive) {
        echo "🚨 CRITICAL: Payment plugin is not activated\n";
        echo "   → This is why orders aren't being created\n";
        echo "   → Activate Payment plugin in admin panel\n\n";
    }
    
    echo "=== NEXT STEPS ===\n";
    
    if ($paystationActive && $debugRouteFound) {
        echo "1. ✅ Visit https://bulbulee.com/payment/paystation/debug\n";
        echo "2. ✅ Run the debug analysis to see detailed issues\n";
        echo "3. ✅ Follow the recommendations to fix order creation\n";
    } else {
        echo "1. 🔧 Activate PayStation plugin in admin panel\n";
        echo "2. 🔧 Clear route cache: php artisan route:clear\n";
        echo "3. 🔧 Then visit https://bulbulee.com/payment/paystation/debug\n";
    }
    
    if (!$paymentActive) {
        echo "4. 🚨 MOST IMPORTANT: Activate Payment plugin in admin panel\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking routes: " . $e->getMessage() . "\n";
}

echo "\nRoute check completed!\n";
