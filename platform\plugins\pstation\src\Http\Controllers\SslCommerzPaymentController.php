<?php

namespace Bo<PERSON>ble\Pstation\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Botble\Payment\Supports\PaymentHelper;
use Botble\Pstation\Http\Requests\PaymentRequest;
use Botble\Pstation\Library\Pstation\PstationNotification;

class PayStationPaymentController extends BaseController
{
    public function success(PaymentRequest $request, BaseHttpResponse $response)
    {
        $transactionId = $request->input('tran_id');
        $amount = $request->input('amount');
        $currency = $request->input('currency');
        $checkoutToken = $request->input('value_b');

        $gateway = new PstationNotification();
        $validation = $gateway->orderValidate($request->input(), $transactionId, $amount, $currency);

        if (! $validation) {
            return $response
                ->setError()
                ->setNextUrl(PaymentHelper::getCancelURL($checkoutToken))
                ->setMessage(__('Payment failed!'));
        }

        $orderIds = explode(';', $request->input('value_a'));

        do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
            'amount' => $request->input('amount'),
            'currency' => $currency,
            'charge_id' => $transactionId,
            'payment_channel' => PSTATION_PAYMENT_METHOD_NAME,
            'status' => PaymentStatusEnum::COMPLETED,
            'customer_id' => $request->input('value_c'),
            'customer_type' => urldecode($request->input('value_d')),
            'payment_type' => 'direct',
            'order_id' => $orderIds,
        ]);

        return $response
            ->setNextUrl(PaymentHelper::getRedirectURL($checkoutToken))
            ->setMessage(__('Checkout successfully!'));
    }

    public function fail(PaymentRequest $request, BaseHttpResponse $response)
    {
        $checkoutToken = $request->input('value_b');

        return $response
            ->setError()
            ->setNextUrl(PaymentHelper::getCancelURL($checkoutToken))
            ->setMessage(__('Payment failed!'));
    }

    public function cancel(PaymentRequest $request, BaseHttpResponse $response)
    {
        $checkoutToken = $request->input('value_b');

        return $response
            ->setError()
            ->setNextUrl(PaymentHelper::getCancelURL($checkoutToken))
            ->setMessage(__('Payment failed!'));
    }

    public function ipn(PaymentRequest $request, BaseHttpResponse $response)
    {
        if (! $request->input('tran_id')) {
            return $response
                ->setError()
                ->setMessage(__('Invalid Data!'));
        }

        $transactionId = $request->input('tran_id');
        $transaction = Payment::query()->where('charge_id', $transactionId)
            ->select(['charge_id', 'status'])->first();

        if (! $transaction) {
            return $response
                ->setError()
                ->setMessage(__('Invalid Transaction!'));
        }

        $gateway = new PstationNotification();
        $validation = $gateway->orderValidate(
            $request->all(),
            $transactionId,
            $transaction->amount,
            $transaction->currency
        );

        if ($validation) {
            Payment::query()
                ->where('charge_id', $transactionId)
                ->update(['status' => PaymentStatusEnum::COMPLETED]);

            return $response
                ->setError()
                ->setMessage(__('Transaction is successfully completed!'));
        }

        Payment::query()
            ->where('charge_id', $transactionId)
            ->update(['status' => PaymentStatusEnum::FAILED]);

        return $response
            ->setError()
            ->setMessage(__('Validation Fail!'));
    }
}
