<?php
// PayStation Callback Test Script
// Place this in your public directory and configure it as callback URL temporarily
// URL: https://yourdomain.com/test-callback.php

// Log all incoming requests
$logFile = '../storage/logs/paystation-callback-test.log';
$timestamp = date('Y-m-d H:i:s');

// Get all request data
$method = $_SERVER['REQUEST_METHOD'];
$headers = getallheaders();
$getParams = $_GET;
$postParams = $_POST;
$rawInput = file_get_contents('php://input');

// Create log entry
$logEntry = [
    'timestamp' => $timestamp,
    'method' => $method,
    'headers' => $headers,
    'get_params' => $getParams,
    'post_params' => $postParams,
    'raw_input' => $rawInput,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
];

// Write to log file
file_put_contents($logFile, json_encode($logEntry, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);

// Respond to PayStation
http_response_code(200);
echo "OK - Callback received and logged";

// Also display the data for debugging (remove in production)
if (isset($_GET['debug'])) {
    echo "<h1>PayStation Callback Test</h1>";
    echo "<h2>Request Method: $method</h2>";
    echo "<h3>Headers:</h3><pre>" . print_r($headers, true) . "</pre>";
    echo "<h3>GET Parameters:</h3><pre>" . print_r($getParams, true) . "</pre>";
    echo "<h3>POST Parameters:</h3><pre>" . print_r($postParams, true) . "</pre>";
    echo "<h3>Raw Input:</h3><pre>" . htmlspecialchars($rawInput) . "</pre>";
    echo "<p><strong>This data has been logged to:</strong> $logFile</p>";
}
?>
