<?php

require_once 'vendor/autoload.php';

echo "PayStation Plugin Manual Activation\n";
echo "===================================\n\n";

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    // Check if the plugin management system exists
    if (class_exists('Botble\PluginManagement\Services\PluginService')) {
        echo "1. Plugin management system found\n";
        
        $pluginService = app('Botble\PluginManagement\Services\PluginService');
        
        // Check if PayStation plugin exists
        $pluginPath = 'paystation';
        
        echo "2. Checking plugin status...\n";
        
        // Try to activate the plugin
        try {
            $result = $pluginService->activate($pluginPath);
            if ($result['error']) {
                echo "   ✗ Activation failed: " . $result['message'] . "\n";
            } else {
                echo "   ✓ Plugin activated successfully!\n";
            }
        } catch (Exception $e) {
            echo "   ✗ Activation error: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "1. Plugin management system not found\n";
        echo "   Trying alternative activation method...\n";
        
        // Alternative: Direct database activation
        if (class_exists('Botble\Setting\Facades\Setting')) {
            echo "2. Using Settings to activate plugin...\n";
            
            // Get current activated plugins
            $activatedPlugins = setting('activated_plugins', []);
            
            if (!in_array('paystation', $activatedPlugins)) {
                $activatedPlugins[] = 'paystation';
                setting()->set('activated_plugins', $activatedPlugins)->save();
                echo "   ✓ Plugin added to activated plugins list\n";
            } else {
                echo "   ✓ Plugin already in activated plugins list\n";
            }
        } else {
            echo "   ✗ Settings system not available\n";
        }
    }
    
    echo "\n3. Clearing caches...\n";
    
    // Clear various caches
    try {
        Artisan::call('optimize:clear');
        echo "   ✓ Application caches cleared\n";
    } catch (Exception $e) {
        echo "   ✗ Cache clear failed: " . $e->getMessage() . "\n";
    }
    
    try {
        Artisan::call('config:clear');
        echo "   ✓ Configuration cache cleared\n";
    } catch (Exception $e) {
        echo "   ✗ Config clear failed: " . $e->getMessage() . "\n";
    }
    
    try {
        Artisan::call('route:clear');
        echo "   ✓ Route cache cleared\n";
    } catch (Exception $e) {
        echo "   ✗ Route clear failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. Verifying activation...\n";
    
    // Check if the service provider is now loaded
    $providers = app()->getLoadedProviders();
    $paystationProvider = 'Botble\\PayStation\\Providers\\PayStationServiceProvider';
    
    if (isset($providers[$paystationProvider])) {
        echo "   ✓ PayStationServiceProvider is now loaded\n";
    } else {
        echo "   ✗ PayStationServiceProvider is still not loaded\n";
        echo "     You may need to restart your web server\n";
    }
    
    echo "\nActivation process completed!\n";
    echo "\nNext steps:\n";
    echo "1. Restart your web server if needed\n";
    echo "2. Go to Admin Panel > Plugins to verify activation\n";
    echo "3. Configure PayStation settings in Admin Panel > Payment\n";
    echo "4. Test the payment integration\n";
    
} catch (Exception $e) {
    echo "Error during activation: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nIf manual activation doesn't work:\n";
echo "1. Go to Admin Panel > Plugins\n";
echo "2. Find PayStation in the plugin list\n";
echo "3. Click 'Activate' button\n";
echo "4. Configure the payment settings\n";
