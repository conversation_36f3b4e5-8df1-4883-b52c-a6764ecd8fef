<?php

namespace Botble\PayStation\Services\Abstracts;

use Botble\Payment\Services\Traits\PaymentErrorTrait;
use Botble\Support\Services\ProduceServiceInterface;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

abstract class PayStationPaymentAbstract implements ProduceServiceInterface
{
    use PaymentErrorTrait;

    protected string $paymentCurrency;

    protected bool $supportRefundOnline;

    protected float $totalAmount;

    protected string $merchantId;

    protected string $password;

    public function __construct()
    {
        $this->paymentCurrency = config('plugins.payment.payment.currency', 'BDT');

        $this->totalAmount = 0;

        $this->setCredentials();

        $this->supportRefundOnline = false; // PayStation doesn't support online refunds
    }

    public function getSupportRefundOnline(): bool
    {
        return $this->supportRefundOnline;
    }

    public function setCredentials(): self
    {
        $this->merchantId = get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME);
        $this->password = get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME);

        return $this;
    }

    public function setCurrency(string $currency): self
    {
        $this->paymentCurrency = $currency;

        return $this;
    }

    public function getCurrency(): string
    {
        return $this->paymentCurrency;
    }

    public function setTotalAmount(float $amount): self
    {
        $this->totalAmount = $amount;

        return $this;
    }

    public function getTotalAmount(): float
    {
        return $this->totalAmount;
    }

    public function getWebhookUrl(): string
    {
        return route('payments.paystation.webhook');
    }

    public function getCallbackUrl(): string
    {
        return route('payments.paystation.callback');
    }

    public function checkTransactionStatus(string $invoiceNumber): array
    {
        try {
            // According to official API documentation, merchantId goes in header
            $response = Http::timeout(60)
                ->withHeaders([
                    'merchantId' => $this->merchantId
                ])
                ->post('https://api.paystation.com.bd/transaction-status', [
                    'invoice_number' => $invoiceNumber
                ]);

            if ($response->failed()) {
                return [
                    'success' => false,
                    'message' => 'Failed to check transaction status',
                    'data' => null
                ];
            }

            $result = $response->json();

            // Check response according to official API documentation
            $isSuccess = isset($result['status_code']) && $result['status_code'] === '200' &&
                        isset($result['status']) && $result['status'] === 'success';

            return [
                'success' => $isSuccess,
                'data' => $result['data'] ?? null,
                'message' => $result['message'] ?? 'Unknown response',
                'status_code' => $result['status_code'] ?? null,
                'raw_response' => $result
            ];

        } catch (Exception $exception) {
            return [
                'success' => false,
                'message' => $exception->getMessage(),
                'data' => null,
                'status_code' => null,
                'raw_response' => null
            ];
        }
    }

    public function execute(Request $request)
    {
        try {
            return $this->makePayment($request);
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 1);

            return false;
        }
    }

    abstract public function makePayment(Request $request);

    /**
     * List currencies supported by PayStation
     */
    public function supportedCurrencyCodes(): array
    {
        return [
            'BDT', // Bangladesh Taka - primary currency
        ];
    }

    abstract public function afterMakePayment(Request $request);
}
