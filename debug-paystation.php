<?php

echo "PayStation Plugin Debug Script\n";
echo "==============================\n\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "Error: Please run this script from the Laravel root directory\n";
    exit(1);
}

// Check plugin files
echo "1. Checking Plugin Files:\n";
$pluginDir = 'platform/plugins/paystation';
$requiredFiles = [
    'plugin.json',
    'src/Providers/PayStationServiceProvider.php',
    'src/Providers/HookServiceProvider.php',
    'src/Services/Gateways/PayStationPaymentService.php',
    'src/Forms/PayStationPaymentMethodForm.php',
    'src/Http/Controllers/PayStationController.php',
    'helpers/constants.php',
    'routes/web.php',
    'resources/views/methods.blade.php',
    'resources/views/instructions.blade.php'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    $filePath = $pluginDir . '/' . $file;
    if (file_exists($filePath)) {
        echo "   ✓ $file\n";
    } else {
        echo "   ✗ $file (MISSING)\n";
        $missingFiles[] = $file;
    }
}

if (!empty($missingFiles)) {
    echo "\nError: Missing files found. Please ensure all plugin files are present.\n";
    exit(1);
}

// Check PHP syntax
echo "\n2. Checking PHP Syntax:\n";
$phpFiles = [
    'src/Providers/PayStationServiceProvider.php',
    'src/Providers/HookServiceProvider.php',
    'src/Services/Gateways/PayStationPaymentService.php',
    'src/Forms/PayStationPaymentMethodForm.php',
    'src/Http/Controllers/PayStationController.php',
    'src/Plugin.php'
];

foreach ($phpFiles as $file) {
    $filePath = $pluginDir . '/' . $file;
    $output = [];
    $returnCode = 0;
    exec("php -l \"$filePath\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✓ $file\n";
    } else {
        echo "   ✗ $file (SYNTAX ERROR)\n";
        echo "     " . implode("\n     ", $output) . "\n";
    }
}

// Check if constants are defined
echo "\n3. Checking Constants:\n";
$constantsFile = $pluginDir . '/helpers/constants.php';
if (file_exists($constantsFile)) {
    include_once $constantsFile;
    if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
        echo "   ✓ PAYSTATION_PAYMENT_METHOD_NAME: " . PAYSTATION_PAYMENT_METHOD_NAME . "\n";
    } else {
        echo "   ✗ PAYSTATION_PAYMENT_METHOD_NAME not defined\n";
    }
} else {
    echo "   ✗ Constants file missing\n";
}

// Check routes
echo "\n4. Checking Routes:\n";
$routesFile = $pluginDir . '/routes/web.php';
if (file_exists($routesFile)) {
    $routesContent = file_get_contents($routesFile);
    if (strpos($routesContent, 'paystation.callback') !== false) {
        echo "   ✓ Callback route defined\n";
    } else {
        echo "   ✗ Callback route missing\n";
    }
    if (strpos($routesContent, 'paystation.webhook') !== false) {
        echo "   ✓ Webhook route defined\n";
    } else {
        echo "   ✗ Webhook route missing\n";
    }
} else {
    echo "   ✗ Routes file missing\n";
}

// Check public assets
echo "\n5. Checking Public Assets:\n";
$publicDir = 'public/vendor/core/plugins/paystation';
if (is_dir($publicDir)) {
    echo "   ✓ Public directory exists\n";
    if (is_dir($publicDir . '/images')) {
        echo "   ✓ Images directory exists\n";
    } else {
        echo "   ✗ Images directory missing\n";
    }
} else {
    echo "   ✗ Public directory missing\n";
    echo "     Run: mkdir -p public/vendor/core/plugins/paystation/images\n";
}

echo "\n6. Common Issues & Solutions:\n";
echo "   - Plugin not activated: Go to Admin Panel > Plugins and activate PayStation\n";
echo "   - Missing configuration: Configure Merchant ID and Password in Payment settings\n";
echo "   - Route conflicts: Clear route cache with 'php artisan route:clear'\n";
echo "   - Autoload issues: Run 'composer dump-autoload'\n";
echo "   - Cache issues: Clear all caches with 'php artisan optimize:clear'\n";

echo "\n7. Next Steps:\n";
echo "   1. Ensure plugin is activated in admin panel\n";
echo "   2. Configure PayStation settings (Merchant ID, Password)\n";
echo "   3. Test with PayStation sandbox credentials first\n";
echo "   4. Check server logs for specific error messages\n";
echo "   5. Verify callback URLs are accessible from PayStation servers\n";

echo "\nDebug complete!\n";
