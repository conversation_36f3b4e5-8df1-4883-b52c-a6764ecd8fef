<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayStation Debug - Order Creation Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .step { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step-title { font-weight: bold; font-size: 16px; margin-bottom: 10px; }
        .details { background: #f8f9fa; padding: 10px; border-radius: 3px; margin-top: 10px; }
        .recommendations { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .priority-high { color: #dc3545; font-weight: bold; }
        .priority-medium { color: #ffc107; font-weight: bold; }
        .priority-low { color: #28a745; font-weight: bold; }
        .form-section { background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .quick-actions { display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PayStation Debug - Order Creation Analysis</h1>
            <p>Comprehensive analysis of why orders aren't being created</p>
        </div>

        <div class="quick-actions">
            <button class="btn" onclick="runDebug()">Run Full Debug</button>
            <button class="btn" onclick="runDebugWithCallback()">Run Debug + Simulate Callback</button>
            <button class="btn" onclick="checkLogs()">Check Recent Logs</button>
            <button class="btn" onclick="testCallback()">Test Callback Manually</button>
        </div>

        <div class="form-section">
            <h3>Test Parameters</h3>
            <label>Test Invoice Number:</label>
            <input type="text" id="testInvoice" value="LV1389-1752255805-1752783970-74" style="width: 300px; padding: 5px; margin: 5px;">
            <br>
            <label>Callback Status:</label>
            <select id="callbackStatus" style="padding: 5px; margin: 5px;">
                <option value="Successful">Successful</option>
                <option value="Failed">Failed</option>
                <option value="Canceled">Canceled</option>
            </select>
            <label>Transaction ID:</label>
            <input type="text" id="trxId" value="TEST123" style="width: 200px; padding: 5px; margin: 5px;">
        </div>

        <div id="results">
            <p>Click "Run Full Debug" to analyze why orders aren't being created.</p>
        </div>
    </div>

    <script>
        function runDebug() {
            const testInvoice = document.getElementById('testInvoice').value;
            
            fetch('/payment/paystation/debug?test_invoice=' + encodeURIComponent(testInvoice), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => displayResults(data))
            .catch(error => {
                document.getElementById('results').innerHTML = '<div class="status-error">Error: ' + error.message + '</div>';
            });
        }

        function runDebugWithCallback() {
            const testInvoice = document.getElementById('testInvoice').value;
            const status = document.getElementById('callbackStatus').value;
            const trxId = document.getElementById('trxId').value;
            
            const params = new URLSearchParams({
                test_invoice: testInvoice,
                simulate_callback: '1',
                status: status,
                invoice_number: testInvoice,
                trx_id: trxId
            });
            
            fetch('/payment/paystation/debug?' + params.toString(), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => displayResults(data))
            .catch(error => {
                document.getElementById('results').innerHTML = '<div class="status-error">Error: ' + error.message + '</div>';
            });
        }

        function checkLogs() {
            // This would need to be implemented as a separate endpoint
            alert('Check your Laravel logs at: storage/logs/laravel-' + new Date().toISOString().split('T')[0] + '.log');
        }

        function testCallback() {
            const testInvoice = document.getElementById('testInvoice').value;
            const status = document.getElementById('callbackStatus').value;
            const trxId = document.getElementById('trxId').value;
            
            const params = new URLSearchParams({
                status: status,
                invoice_number: testInvoice,
                trx_id: trxId
            });
            
            const callbackUrl = '/payment/paystation/callback?' + params.toString();
            
            fetch(callbackUrl, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(data => {
                alert('Callback test completed. Check logs for results. Response: ' + data);
            })
            .catch(error => {
                alert('Callback test error: ' + error.message);
            });
        }

        function displayResults(data) {
            let html = '<h2>Debug Results - ' + data.timestamp + '</h2>';
            
            // Display each step
            if (data.steps) {
                Object.keys(data.steps).forEach(stepKey => {
                    const step = data.steps[stepKey];
                    const statusClass = 'status-' + step.status;
                    
                    html += '<div class="step">';
                    html += '<div class="step-title ' + statusClass + '">' + stepKey.replace(/_/g, ' ').toUpperCase() + '</div>';
                    html += '<div class="' + statusClass + '">' + (step.message || 'No message') + '</div>';
                    
                    if (step.details) {
                        html += '<div class="details"><pre>' + JSON.stringify(step.details, null, 2) + '</pre></div>';
                    }
                    
                    html += '</div>';
                });
            }
            
            // Display recommendations
            if (data.recommendations && data.recommendations.length > 0) {
                html += '<div class="recommendations">';
                html += '<h3>🔧 Recommendations to Fix Order Creation</h3>';
                
                data.recommendations.forEach(rec => {
                    const priorityClass = 'priority-' + rec.priority.toLowerCase();
                    html += '<div style="margin-bottom: 10px;">';
                    html += '<span class="' + priorityClass + '">[' + rec.priority + ']</span> ';
                    html += '<strong>' + rec.issue + '</strong><br>';
                    html += '💡 Solution: ' + rec.solution;
                    html += '</div>';
                });
                
                html += '</div>';
            }
            
            document.getElementById('results').innerHTML = html;
        }

        // Auto-run debug on page load
        window.onload = function() {
            runDebug();
        };
    </script>
</body>
</html>
