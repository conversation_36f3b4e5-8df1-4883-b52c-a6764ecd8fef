/* Always visible sticky bar for mobile and tablet */
@media (max-width: 991px) {
    /* Force the sticky bar to be visible */
    #sticky-add-to-cart .sticky-atc-wrap {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 9999 !important;
        transform: none !important;
        background-color: #fff !important;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
        padding: 10px 0 !important;
    }

    /* Hide the original quantity/buttons section */
    .cart-form .product-button,
    .product-details-content .product-button {
        display: none !important;
    }

    /* Style the sticky bar content */
    #sticky-add-to-cart .sticky-atc-btn {
        display: flex !important;
        flex-wrap: wrap !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 0 15px !important;
    }

    /* Style the quantity selector */
    #sticky-add-to-cart .quantity {
        flex: 0 0 100% !important;
        margin-bottom: 10px !important;
        display: flex !important;
        align-items: center !important;
    }

    #sticky-add-to-cart .quantity .label-quantity {
        margin-right: 10px !important;
        font-weight: 500 !important;
    }

    #sticky-add-to-cart .quantity .qty-box {
        display: flex !important;
        align-items: center !important;
        border: 1px solid #ddd !important;
        border-radius: 3px !important;
    }

    #sticky-add-to-cart .quantity .svg-icon {
        width: 30px !important;
        height: 30px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
    }

    #sticky-add-to-cart .quantity .qty {
        width: 50px !important;
        text-align: center !important;
        border: none !important;
        height: 30px !important;
        padding: 0 !important;
        -moz-appearance: textfield !important;
    }

    #sticky-add-to-cart .quantity .qty::-webkit-outer-spin-button,
    #sticky-add-to-cart .quantity .qty::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        margin: 0 !important;
    }

    /* Style the buttons */
    #sticky-add-to-cart .btn {
        flex: 0 0 48% !important;
        margin: 0 !important;
        padding: 8px 5px !important;
    }

    /* Ensure proper spacing at the bottom of the page */
    .single-product .product-detail-container {
        padding-bottom: 120px !important;
    }

    /* Hide the header--product section */
    .header--product {
        display: none !important;
    }
}
