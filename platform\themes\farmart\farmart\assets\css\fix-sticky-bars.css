/* Fix for sticky bars positioning */
@media (max-width: 991px) {
    /* Position the add-to-cart sticky bar above the footer mobile bar */
    #sticky-add-to-cart .sticky-atc-wrap {
        bottom: 60px !important; /* Increased space for the footer mobile bar */
        z-index: 998 !important; /* Lower than the maximum z-index but still high */
        border-bottom: 1px solid #eee !important; /* Add a border to separate from footer */
    }

    /* Ensure the footer mobile bar is visible */
    .footer-mobile {
        display: block !important;
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 997 !important; /* Lower than the add-to-cart bar */
        background-color: #fff !important;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
        height: 60px !important; /* Fixed height */
    }

    /* Add more bottom padding to the page to accommodate both bars */
    .single-product .product-detail-container {
        padding-bottom: 170px !important; /* Increased padding */
    }

    /* Make both bars more compact */
    #sticky-add-to-cart .sticky-atc-wrap {
        padding: 3px 0 !important;
    }

    .footer-mobile {
        padding: 0 !important; /* Remove padding */
    }

    .footer-mobile .menu--footer {
        margin: 0 !important;
        padding: 0 !important;
        display: flex !important;
        justify-content: space-around !important;
        align-items: center !important;
        height: 100% !important;
    }

    /* Style the footer mobile items */
    .footer-mobile .menu--footer li {
        flex: 1 !important;
        text-align: center !important;
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        padding: 5px 0 !important;
    }

    .footer-mobile .menu--footer li a {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        height: 100% !important;
    }

    /* Reduce the size of icons in the footer mobile bar */
    .footer-mobile .menu--footer li i {
        font-size: 18px !important;
        margin-bottom: 2px !important;
    }

    .footer-mobile .menu--footer li span {
        font-size: 10px !important;
        line-height: 1 !important;
    }

    /* Make the sticky add-to-cart bar more compact */
    #sticky-add-to-cart .sticky-atc-btn {
        display: flex !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
        padding: 0 10px !important;
        width: 100% !important;
    }

    #sticky-add-to-cart .sticky-bar-content {
        display: flex !important;
        align-items: center !important;
        width: 100% !important;
        padding: 0 !important;
    }

    #sticky-add-to-cart .quantity {
        flex: 0 0 auto !important;
        margin-bottom: 0 !important;
        margin-right: 10px !important;
        display: flex !important;
        align-items: center !important;
    }

    #sticky-add-to-cart .quantity .label-quantity {
        margin-right: 5px !important;
        margin-bottom: 0 !important;
        font-size: 13px !important;
    }

    #sticky-add-to-cart .quantity .qty-box {
        display: flex !important;
        align-items: center !important;
        border: 1px solid #ddd !important;
        border-radius: 3px !important;
        height: 28px !important;
    }

    #sticky-add-to-cart .quantity .qty {
        height: 24px !important;
        font-size: 14px !important;
        width: 40px !important;
        text-align: center !important;
        -moz-appearance: textfield !important;
        appearance: textfield !important;
        border: none !important;
        padding: 0 !important;
    }

    /* Hide the spinner buttons in number input */
    #sticky-add-to-cart .quantity .qty::-webkit-outer-spin-button,
    #sticky-add-to-cart .quantity .qty::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        margin: 0 !important;
    }

    #sticky-add-to-cart .quantity .svg-icon {
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
    }

    #sticky-add-to-cart .quantity .svg-icon svg {
        width: 12px !important;
        height: 12px !important;
    }

    #sticky-add-to-cart .btn {
        flex: 1 !important;
        margin: 0 5px !important;
        padding: 6px 5px !important;
        font-size: 13px !important;
        white-space: nowrap !important;
        margin-bottom: 0 !important;
    }
}
