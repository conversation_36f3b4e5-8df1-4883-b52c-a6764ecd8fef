<?php

echo "PayStation Redirect Fix Verification\n";
echo "====================================\n\n";

// Load Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "1. Checking PayStation Plugin Status:\n";

// Check if plugin is activated
$activatedPlugins = setting('activated_plugins', []);

if (in_array('payment', $activatedPlugins)) {
    echo "   ✓ Payment plugin is activated\n";
} else {
    echo "   ✗ Payment plugin is NOT activated\n";
}

if (in_array('paystation', $activatedPlugins)) {
    echo "   ✓ PayStation plugin is activated\n";
} else {
    echo "   ✗ PayStation plugin is NOT activated\n";
}

echo "\n2. Checking PayStation Configuration:\n";

// Load constants
$constantsFile = 'platform/plugins/paystation/helpers/constants.php';
if (file_exists($constantsFile)) {
    include_once $constantsFile;
    
    if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
        echo "   ✓ PayStation constants loaded\n";
        
        if (function_exists('get_payment_setting')) {
            $merchantId = get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME);
            $password = get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME);
            $status = get_payment_setting('status', PAYSTATION_PAYMENT_METHOD_NAME);
            
            echo "   - Merchant ID: " . ($merchantId ? 'SET' : 'NOT SET') . "\n";
            echo "   - Password: " . ($password ? 'SET' : 'NOT SET') . "\n";
            echo "   - Status: " . ($status ? 'ENABLED' : 'DISABLED') . "\n";
        } else {
            echo "   ✗ Payment helper functions not available\n";
        }
    } else {
        echo "   ✗ PayStation constants not defined\n";
    }
} else {
    echo "   ✗ PayStation constants file not found\n";
}

echo "\n3. Checking Code Fix:\n";

// Check if the HookServiceProvider has the correct checkoutUrl fix
$hookProviderFile = 'platform/plugins/paystation/src/Providers/HookServiceProvider.php';
if (file_exists($hookProviderFile)) {
    $content = file_get_contents($hookProviderFile);
    
    if (strpos($content, "data['checkoutUrl']") !== false) {
        echo "   ✓ checkoutUrl fix is applied\n";
    } else {
        echo "   ✗ checkoutUrl fix is NOT applied\n";
    }
    
    if (strpos($content, 'PaymentHelper::storeLocalPayment') !== false) {
        echo "   ✓ Payment storage is implemented\n";
    } else {
        echo "   ✗ Payment storage is NOT implemented\n";
    }
    
    if (strpos($content, "status_code") !== false && strpos($content, "200") !== false) {
        echo "   ✓ Official API response format is used\n";
    } else {
        echo "   ✗ Official API response format is NOT used\n";
    }
} else {
    echo "   ✗ HookServiceProvider file not found\n";
}

echo "\n4. Expected Behavior After Fix:\n";
echo "   ✓ PayStation API call succeeds (already working from your logs)\n";
echo "   ✓ checkoutUrl is set with PayStation payment URL\n";
echo "   ✓ Customer gets redirected to PayStation payment page\n";
echo "   ✓ No more 'Payment failed!' error\n";

echo "\n5. Next Steps:\n";
echo "   1. Ensure both Payment and PayStation plugins are activated\n";
echo "   2. Configure PayStation settings if not already done\n";
echo "   3. Test the payment flow again\n";
echo "   4. Check that customer gets redirected to PayStation\n";

echo "\n6. Debugging Tips:\n";
echo "   - Check Laravel logs for 'PayStation: Setting checkout URL for redirect'\n";
echo "   - Verify the payment URL in logs matches PayStation format\n";
echo "   - Ensure no JavaScript errors on checkout page\n";

echo "\nVerification completed!\n";

echo "\n" . str_repeat("=", 50) . "\n";
echo "SUMMARY OF THE FIX:\n";
echo "==================\n";
echo "Problem: PayStation API was working but redirect was failing\n";
echo "Root Cause: Wrong response format - used 'redirect_url' instead of 'checkoutUrl'\n";
echo "Solution: Changed to use 'checkoutUrl' like Stripe plugin\n";
echo "Expected Result: Customer will now be redirected to PayStation payment page\n";
echo str_repeat("=", 50) . "\n";
