<?php

echo "PayStation Debug System Test\n";
echo "============================\n\n";

// Load Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing PayStation Debug System...\n\n";

try {
    // Test the debug controller directly
    $request = new \Illuminate\Http\Request();
    $request->merge([
        'test_invoice' => 'LV1389-1752255805-1752783970-74',
        'simulate_callback' => '1',
        'status' => 'Successful',
        'invoice_number' => 'LV1389-1752255805-1752783970-74',
        'trx_id' => 'TEST123'
    ]);
    
    // Set request to expect JSON
    $request->headers->set('Accept', 'application/json');
    $request->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    $response = new \Botble\Base\Http\Responses\BaseHttpResponse();
    $debugController = new \Botble\PayStation\Http\Controllers\PayStationDebugController();
    
    $result = $debugController->debugOrderCreation($request, $response);
    $data = $result->getData();
    
    echo "✅ Debug system is working!\n\n";
    
    echo "=== DEBUG ANALYSIS RESULTS ===\n";
    echo "Timestamp: " . $data['timestamp'] . "\n\n";
    
    // Show step results
    foreach ($data['steps'] as $stepName => $stepData) {
        $status = $stepData['status'];
        $statusIcon = $status === 'success' ? '✅' : ($status === 'error' ? '❌' : '⚠️');
        
        echo "$statusIcon " . strtoupper(str_replace('_', ' ', $stepName)) . "\n";
        echo "   Status: $status\n";
        echo "   Message: " . ($stepData['message'] ?? 'No message') . "\n";
        
        if (isset($stepData['details']) && is_array($stepData['details'])) {
            foreach ($stepData['details'] as $key => $value) {
                if (is_bool($value)) {
                    $value = $value ? 'true' : 'false';
                } elseif (is_array($value)) {
                    $value = json_encode($value);
                }
                echo "   $key: $value\n";
            }
        }
        echo "\n";
    }
    
    // Show recommendations
    if (!empty($data['recommendations'])) {
        echo "=== RECOMMENDATIONS TO FIX ORDER CREATION ===\n";
        foreach ($data['recommendations'] as $rec) {
            $priorityIcon = $rec['priority'] === 'HIGH' ? '🚨' : ($rec['priority'] === 'MEDIUM' ? '⚠️' : 'ℹ️');
            echo "$priorityIcon [{$rec['priority']}] {$rec['issue']}\n";
            echo "   💡 Solution: {$rec['solution']}\n\n";
        }
    }
    
    echo "=== ACCESS DEBUG INTERFACE ===\n";
    echo "🌐 Web Interface: https://bulbulee.com/payment/paystation/debug\n";
    echo "🔗 API Endpoint: https://bulbulee.com/payment/paystation/debug (with Accept: application/json)\n\n";
    
    echo "=== QUICK DIAGNOSIS ===\n";
    
    // Quick diagnosis based on results
    $pluginCheck = $data['steps']['1_plugin_check'] ?? null;
    $paymentActionCheck = $data['steps']['7_payment_action_check'] ?? null;
    $orderLookupCheck = $data['steps']['6_order_lookup_test'] ?? null;
    
    if ($pluginCheck && $pluginCheck['status'] === 'error') {
        echo "🚨 CRITICAL: Payment/PayStation plugins are not activated!\n";
        echo "   → Go to Admin Panel > Plugins and activate both plugins\n\n";
    }
    
    if ($paymentActionCheck && isset($paymentActionCheck['details']['payment_action_defined']) && !$paymentActionCheck['details']['payment_action_defined']) {
        echo "🚨 CRITICAL: PAYMENT_ACTION_PAYMENT_PROCESSED not defined!\n";
        echo "   → This means the Payment plugin is not properly loaded\n\n";
    }
    
    if ($orderLookupCheck && $orderLookupCheck['status'] === 'error') {
        echo "⚠️ WARNING: Order lookup is failing!\n";
        echo "   → Check if orders are being created during checkout\n\n";
    }
    
    // Check if callback simulation would work
    $callbackSim = $data['steps']['8_callback_simulation'] ?? null;
    if ($callbackSim) {
        if ($callbackSim['status'] === 'success') {
            echo "✅ GOOD: Callback simulation successful - orders should be created when PayStation sends callbacks\n\n";
        } else {
            echo "❌ PROBLEM: Callback simulation failed - orders will not be created\n";
            echo "   → Fix the issues identified above\n\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error testing debug system: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "=== NEXT STEPS ===\n";
echo "1. Visit https://bulbulee.com/payment/paystation/debug in your browser\n";
echo "2. Click 'Run Full Debug' to see detailed analysis\n";
echo "3. Follow the recommendations to fix order creation\n";
echo "4. Test with a real payment after fixing issues\n";
echo "5. Monitor logs during payment: tail -f storage/logs/laravel-" . date('Y-m-d') . ".log\n\n";

echo "Debug system test completed!\n";
