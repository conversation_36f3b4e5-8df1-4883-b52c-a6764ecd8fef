# PayStation Setup for bulbulee.com

## 🎯 **Immediate Action Required**

### **Step 1: Configure PayStation Dashboard**

Login to your PayStation merchant dashboard and configure these URLs:

1. **Callback URL:** `https://bulbulee.com/payment/paystation/callback`
2. **Webhook URL:** `https://bulbulee.com/payment/paystation/webhook`
3. **Success URL:** `https://bulbulee.com/checkout/success`
4. **Fail URL:** `https://bulbulee.com/checkout/failure`

### **Step 2: Test Callback Accessibility**

I've created a test script. Run this to verify your callback URL is accessible:

```bash
curl -I https://bulbulee.com/payment/paystation/callback
```

Expected response: `HTTP/1.1 400 Bad Request` (this is normal - it means the route exists)

### **Step 3: Run Debug Script**

```bash
php debug-live-callback.php
```

This will check:
- ✅ Callback URL: `https://bulbulee.com/payment/paystation/callback`
- ✅ Payment records in database
- ✅ Order records in database
- ✅ Recent PayStation activity

### **Step 4: Test Callback Reception**

**Option A: Use Test Script (Recommended)**
1. Temporarily configure this in PayStation dashboard:
   ```
   https://bulbulee.com/test-callback.php
   ```

2. Make a test payment

3. Check the log:
   ```bash
   cat storage/logs/paystation-callback-test.log
   ```

**Option B: Monitor Live Logs**
```bash
tail -f storage/logs/laravel-$(date +%Y-%m-%d).log | grep -i paystation
```

## 🔧 **Manual Test Commands**

### Test Callback Manually:
```bash
curl -X POST "https://bulbulee.com/payment/paystation/callback?status=Successful&invoice_number=LV1389-1752255805-1752783970-74&trx_id=TEST123"
```

### Check Database Records:
```sql
-- Check recent PayStation payments
SELECT * FROM payments WHERE payment_channel = 'paystation' ORDER BY created_at DESC LIMIT 5;

-- Check recent orders
SELECT id, status, amount, token, is_finished, payment_id, created_at 
FROM ec_orders ORDER BY created_at DESC LIMIT 5;
```

## 📋 **Expected PayStation Dashboard Configuration**

### **Merchant Settings:**
- **Merchant ID:** [Your PayStation Merchant ID]
- **Password:** [Your PayStation Password]
- **Environment:** Live (not sandbox)

### **URL Configuration:**
- **Callback URL:** `https://bulbulee.com/payment/paystation/callback`
- **Webhook URL:** `https://bulbulee.com/payment/paystation/webhook`
- **Success Return URL:** `https://bulbulee.com/checkout/success`
- **Cancel/Fail URL:** `https://bulbulee.com/checkout/failure`

### **Security Settings:**
- **IP Whitelist:** Add your server IP if required
- **SSL Certificate:** Ensure bulbulee.com has valid SSL

## 🚨 **Common Issues for bulbulee.com**

### Issue 1: SSL Certificate
PayStation requires HTTPS. Verify:
```bash
curl -I https://bulbulee.com
```
Should return `HTTP/1.1 200 OK` with valid SSL.

### Issue 2: Server Firewall
Ensure your server accepts incoming POST requests from PayStation.

### Issue 3: Route Not Found
If you get 404 errors, clear Laravel caches:
```bash
php artisan route:clear
php artisan config:clear
php artisan optimize:clear
```

## 🧪 **Testing Procedure for bulbulee.com**

### **Step 1: Verify Plugin Status**
```bash
php test-paystation-integration.php
```

### **Step 2: Test Callback Reception**
1. Configure test callback: `https://bulbulee.com/test-callback.php`
2. Make small test payment (10 BDT)
3. Check if callback is received

### **Step 3: Test Full Flow**
1. Configure real callback: `https://bulbulee.com/payment/paystation/callback`
2. Monitor logs: `tail -f storage/logs/laravel-$(date +%Y-%m-%d).log`
3. Make test payment
4. Look for these log entries:
   - `PayStation: Callback received`
   - `PayStation: Starting order lookup`
   - `PayStation: Payment processed action completed`

### **Step 4: Verify Order Creation**
Check admin panel for:
- ✅ Order appears in orders list
- ✅ Order status is "completed"
- ✅ Payment record is linked
- ✅ Customer receives confirmation

## 📞 **PayStation Support Contact**

If callbacks are not being received:
1. **Contact PayStation Support**
2. **Provide them with:**
   - Your merchant ID
   - Callback URL: `https://bulbulee.com/payment/paystation/callback`
   - Request they verify the URL is reachable from their servers

## ✅ **Success Indicators**

You'll know it's working when you see:
1. **In PayStation dashboard:** Successful callback configuration
2. **In Laravel logs:** `PayStation: Callback received`
3. **In admin panel:** Orders appearing after payment
4. **For customers:** Order confirmation emails

## 🔄 **Quick Fix Checklist**

- [ ] PayStation dashboard configured with bulbulee.com URLs
- [ ] SSL certificate valid for bulbulee.com
- [ ] Server firewall allows incoming connections
- [ ] Laravel routes cached cleared
- [ ] Payment and PayStation plugins activated
- [ ] Test callback script shows reception
- [ ] Live callback shows in Laravel logs

Run the debug script first, then follow the testing procedure. The enhanced logging will show exactly where any issues occur!
