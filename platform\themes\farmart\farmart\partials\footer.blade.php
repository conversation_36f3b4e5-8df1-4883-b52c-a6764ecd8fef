    <!-- Hide footer on mobile and tablet -->
    <style>
        @media (max-width: 991px) {
            #footer {
                display: none !important;
            }
        }
    </style>
    <footer id="footer">
        @if ($preFooterSidebar = dynamic_sidebar('pre_footer_sidebar'))
            <div class="footer-info border-top">
                <div class="container-xxxl py-3">
                    {!! $preFooterSidebar !!}
                </div>
            </div>
        @endif
        @if ($footerSidebar = dynamic_sidebar('footer_sidebar'))
            <div class="footer-widgets">
                <div class="container-xxxl">
                    <div class="row border-top py-5">
                        {!! $footerSidebar !!}
                    </div>
                </div>
            </div>
        @endif
        @if ($bottomFooterSidebar = dynamic_sidebar('bottom_footer_sidebar'))
            <div class="container-xxxl">
                <div
                    class="footer__links"
                    id="footer-links"
                >
                    {!! $bottomFooterSidebar !!}
                </div>
            </div>
        @endif
        <div class="container-xxxl">
            <div class="row border-top py-4">
                <div class="col-lg-3 col-md-4 py-3">
                    <div class="copyright d-flex justify-content-center justify-content-md-start">
                        <span>{!! Theme::getSiteCopyright() !!}</span>
                    </div>
                </div>
                <div class="col-lg-6 col-md-4 py-3">
                    @if (theme_option('payment_methods_image'))
                        <div class="footer-payments d-flex justify-content-center" style="padding: 20px 0;">
                            @if (theme_option('payment_methods_link'))
                                <a
                                    href="{{ url(theme_option('payment_methods_link')) }}"
                                    target="_blank"
                                >
                            @endif

                            <img
                                class="lazyload"
                                data-src="{{ RvMedia::getImageUrl(theme_option('payment_methods_image')) }}"
                                alt="footer-payments"
                                style="max-width: 100%; height: auto; max-height: 120px;"
                            >

                            @if (theme_option('payment_methods_link'))
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
                <div class="col-lg-3 col-md-4 py-3">
                    <div class="footer-socials d-flex justify-content-md-end justify-content-center">
                        @if ($socialLinks = Theme::getSocialLinks())
                            <p class="me-3 mb-0">{{ __('Stay connected:') }}</p>
                            <div class="footer-socials-container">
                                <ul class="ps-0 mb-0">
                                    @foreach($socialLinks as $socialLink)
                                        @continue(! $socialLink->getUrl() || ! $socialLink->getIconHtml())

                                        <li class="d-inline-block ps-1 my-1">
                                            <a {!! $socialLink->getAttributes() !!}>{{ $socialLink->getIconHtml() }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </footer>
    @if (is_plugin_active('ecommerce'))
        <div
            class="panel--sidebar"
            id="navigation-mobile"
        >
            <div class="panel__header">
                <span class="svg-icon close-toggle--sidebar">
                    <svg>
                        <use
                            href="#svg-icon-arrow-left"
                            xlink:href="#svg-icon-arrow-left"
                        ></use>
                    </svg>
                </span>
                <h3>{{ __('Categories') }}</h3>
            </div>
            <div
                class="panel__content"
                data-bb-toggle="init-categories-dropdown"
                data-bb-target=".product-category-dropdown-wrapper"
                data-url="{{ route('public.ajax.categories-dropdown') }}"
            >
                <ul class="menu--mobile product-category-dropdown-wrapper"></ul>
            </div>
        </div>
    @endif

    <div
        class="panel--sidebar"
        id="menu-mobile"
    >
        <div class="panel__header">
            <span class="svg-icon close-toggle--sidebar">
                <svg>
                    <use
                        href="#svg-icon-arrow-left"
                        xlink:href="#svg-icon-arrow-left"
                    ></use>
                </svg>
            </span>
            <h3>{{ __('Menu') }}</h3>
        </div>
        <div class="panel__content">
            {!! Menu::renderMenuLocation('main-menu', [
                'view' => 'menu',
                'options' => ['class' => 'menu--mobile'],
            ]) !!}

            {!! Menu::renderMenuLocation('header-navigation', [
                'view' => 'menu',
                'options' => ['class' => 'menu--mobile'],
            ]) !!}

            <ul class="menu--mobile">

                @if (is_plugin_active('ecommerce'))


                    @if (count($currencies) > 1)
                        <li class="menu-item-has-children">
                            <a href="#">
                                <span>{{ get_application_currency()->title }}</span>
                                <span class="sub-toggle">
                                    <span class="svg-icon">
                                        <svg>
                                            <use
                                                href="#svg-icon-chevron-down"
                                                xlink:href="#svg-icon-chevron-down"
                                            ></use>
                                        </svg>
                                    </span>
                                </span>
                            </a>
                            <ul class="sub-menu">
                                @foreach ($currencies as $currency)
                                    @if ($currency->id !== get_application_currency_id())
                                        <li><a
                                                href="{{ route('public.change-currency', $currency->title) }}"><span>{{ $currency->title }}</span></a>
                                        </li>
                                    @endif
                                @endforeach
                            </ul>
                        </li>
                    @endif
                @endif
                @if (is_plugin_active('language'))
                    @php
                        $supportedLocales = Language::getSupportedLocales();
                    @endphp

                    @if ($supportedLocales && count($supportedLocales) > 1)
                        @php
                            $languageDisplay = setting('language_display', 'all');
                        @endphp
                        <li class="menu-item-has-children">
                            <a href="#">
                                @if ($languageDisplay == 'all' || $languageDisplay == 'flag')
                                    {!! language_flag(Language::getCurrentLocaleFlag(), Language::getCurrentLocaleName()) !!}
                                @endif
                                @if ($languageDisplay == 'all' || $languageDisplay == 'name')
                                    {{ Language::getCurrentLocaleName() }}
                                @endif
                                <span class="sub-toggle">
                                    <span class="svg-icon">
                                        <svg>
                                            <use
                                                href="#svg-icon-chevron-down"
                                                xlink:href="#svg-icon-chevron-down"
                                            ></use>
                                        </svg>
                                    </span>
                                </span>
                            </a>
                            <ul class="sub-menu">
                                @foreach ($supportedLocales as $localeCode => $properties)
                                    @if ($localeCode != Language::getCurrentLocale())
                                        <li>
                                            <a
                                                href="{{ Language::getSwitcherUrl($localeCode, $properties['lang_code']) }}">
                                                @if ($languageDisplay == 'all' || $languageDisplay == 'flag')
                                                    {!! language_flag($properties['lang_flag'], $properties['lang_name']) !!}
                                                @endif
                                                @if ($languageDisplay == 'all' || $languageDisplay == 'name')
                                                    <span>{{ $properties['lang_name'] }}</span>
                                                @endif
                                            </a>
                                        </li>
                                    @endif
                                @endforeach
                            </ul>
                        </li>
                    @endif
                @endif
            </ul>
        </div>
    </div>
    <div
        class="panel--sidebar panel--sidebar__right"
        id="search-mobile"
    >
        <div class="panel__header">
            @if (is_plugin_active('ecommerce'))
                <x-plugins-ecommerce::fronts.ajax-search class="form--quick-search bb-form-quick-search w-100">
                    <div class="search-inner-content">
                        <div class="text-search">
                            <div class="search-wrapper">
                                <x-plugins-ecommerce::fronts.ajax-search.input type="text" class="search-field input-search-product" />
                                <button
                                    class="btn"
                                    type="submit"
                                    aria-label="Submit"
                                >
                                    <span class="svg-icon">
                                        <svg>
                                            <use
                                                href="#svg-icon-search"
                                                xlink:href="#svg-icon-search"
                                            ></use>
                                        </svg>
                                    </span>
                                </button>
                            </div>
                            <a
                                class="close-search-panel close-toggle--sidebar"
                                href="#"
                                aria-label="Search"
                            >
                                <span class="svg-icon">
                                    <svg>
                                        <use
                                            href="#svg-icon-times"
                                            xlink:href="#svg-icon-times"
                                        ></use>
                                    </svg>
                                </span>
                            </a>
                        </div>
                    </div>
                </x-plugins-ecommerce::fronts.ajax-search>
            @endif
        </div>
    </div>
    <div class="footer-mobile">
        <ul class="menu--footer">
            <li>
                <a href="{{ BaseHelper::getHomepageUrl() }}">
                    <i class="icon-home3"></i>
                    <span>{{ __('Home') }}</span>
                </a>
            </li>
            @if (is_plugin_active('ecommerce'))
                <li>
                    <a
                        class="toggle--sidebar"
                        href="#navigation-mobile"
                    >
                        <i class="icon-list"></i>
                        <span>{{ __('Category') }}</span>
                    </a>
                </li>
                @if (EcommerceHelper::isCartEnabled())
                    <li>
                        <a
                            class="toggle--sidebar"
                            href="#cart-mobile"
                        >
                            <i class="icon-cart">
                                <span class="cart-counter">{{ Cart::instance('cart')->count() }}</span>
                            </i>
                            <span>{{ __('Cart') }}</span>
                        </a>
                    </li>
                @endif
                @if (EcommerceHelper::isWishlistEnabled())
                    <li>
                        <a href="{{ route('public.wishlist') }}">
                            <i class="icon-heart"></i>
                            <span>{{ __('Wishlist') }}</span>
                        </a>
                    </li>
                @endif
                <li>
                    <a href="{{ route('customer.overview') }}">
                        <i class="icon-user"></i>
                        <span>{{ __('Account') }}</span>
                    </a>
                </li>
            @endif
        </ul>
    </div>
    @if (is_plugin_active('ecommerce'))
        {!! Theme::partial('ecommerce.quick-view-modal') !!}
    @endif
    {!! Theme::partial('toast') !!}

    <div class="panel-overlay-layer"></div>

    <!-- Product text colors script -->
    <script src="{{ asset('themes/farmart/js/product-text-colors.js') }}"></script>
    <!-- Countdown timer script -->
    <script src="{{ asset('themes/farmart/plugins/expire-countdown.js') }}"></script>
    <div id="back2top">
        <span class="svg-icon">
            <svg>
                <use
                    href="#svg-icon-arrow-up"
                    xlink:href="#svg-icon-arrow-up"
                ></use>
            </svg>
        </span>
    </div>

    <script>
        'use strict';

        window.trans = {
            "View All": "{{ __('View All') }}",
            "No reviews!": "{{ __('No reviews!') }}"
        };

        window.siteConfig = {
            "url": "{{ BaseHelper::getHomepageUrl() }}",
            "img_placeholder": "{{ theme_option('lazy_load_image_enabled', 'yes') == 'yes' ? image_placeholder() : null }}",
            "countdown_text": {
                "days": "{{ __('days') }}",
                "hours": "{{ __('hours') }}",
                "minutes": "{{ __('mins') }}",
                "seconds": "{{ __('secs') }}"
            }
        };

        @if (is_plugin_active('ecommerce') && EcommerceHelper::isCartEnabled())
            window.siteConfig.ajaxCart = "{{ route('public.ajax.cart') }}";
            window.siteConfig.cartUrl = "{{ route('public.cart') }}";
        @endif

        // Fix for checkout page price display
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're on the checkout page
            if (document.body.classList.contains('checkout-page')) {
                // First, directly target and remove any "Price: $XX.XX" elements
                const removeUnwantedPriceElements = function() {
                    // Target all elements that might contain the price section
                    const allElements = document.querySelectorAll('.small, small, div, p, span');

                    allElements.forEach(function(element) {
                        // Check if the element contains "Price:" text
                        if (element.textContent && element.textContent.includes('Price:')) {
                            // Check if it's the specific price section we want to remove
                            if (element.textContent.includes('$23,000.00') ||
                                element.textContent.match(/Price:\s+\$[\d,]+\.\d{2}/)) {
                                element.style.display = 'none';
                                element.style.visibility = 'hidden';
                                element.style.height = '0';
                                element.style.width = '0';
                                element.style.overflow = 'hidden';
                                element.style.margin = '0';
                                element.style.padding = '0';
                            }
                        }
                    });

                    // Specifically target the Price: $23,000.00 section
                    // This is a more direct approach that doesn't rely on CSS selectors
                    const priceLabels = document.querySelectorAll('.small.d-flex.justify-content-between');
                    priceLabels.forEach(function(element) {
                        const spans = element.querySelectorAll('span');
                        spans.forEach(function(span) {
                            if (span.textContent && span.textContent.includes('Price:')) {
                                // Found the exact price label, remove its parent
                                let parent = element;
                                parent.style.display = 'none';
                                parent.style.visibility = 'hidden';
                                parent.style.height = '0';
                                parent.style.width = '0';
                                parent.style.overflow = 'hidden';
                                parent.style.margin = '0';
                                parent.style.padding = '0';
                            }
                        });
                    });

                    // Also target any div.small elements that might contain the price
                    const smallDivs = document.querySelectorAll('div.small');
                    smallDivs.forEach(function(element) {
                        element.style.display = 'none';
                        element.style.visibility = 'hidden';
                        element.style.height = '0';
                        element.style.width = '0';
                        element.style.overflow = 'hidden';
                        element.style.margin = '0';
                        element.style.padding = '0';
                    });
                };

                // EXTREMELY AGGRESSIVE approach to remove the price section
                const removeAllPriceSections = function() {
                    // Target the exact price section format we're seeing
                    const exactPriceFormat = /Price:\s+\$[\d,]+\.\d{2}/;

                    // Find ALL elements in the document
                    const allElements = document.querySelectorAll('*');

                    allElements.forEach(function(element) {
                        // Check if this element or any of its children contain the price text
                        if (element.textContent && (
                            element.textContent.includes('Price:') ||
                            exactPriceFormat.test(element.textContent)
                        )) {
                            // If it's the exact format we're looking for, remove it completely
                            if (element.textContent.trim() === 'Price:' ||
                                element.textContent.trim() === 'Price: $23,000.00' ||
                                element.textContent.trim().match(/^Price:\s+\$[\d,]+\.\d{2}$/)) {

                                // Try to find the parent container and remove it
                                let parent = element;
                                for (let i = 0; i < 5; i++) { // Check up to 5 levels up
                                    if (parent.parentElement) {
                                        parent = parent.parentElement;
                                        // If we find a div that contains only this price text, remove it
                                        if (parent.textContent.trim().match(/^Price:\s+\$[\d,]+\.\d{2}$/) ||
                                            parent.textContent.trim() === 'Price: $23,000.00') {
                                            parent.style.display = 'none';
                                            parent.style.visibility = 'hidden';
                                            parent.style.height = '0';
                                            parent.style.width = '0';
                                            parent.style.overflow = 'hidden';
                                            parent.style.margin = '0';
                                            parent.style.padding = '0';
                                            parent.style.position = 'absolute';
                                            parent.style.left = '-9999px';
                                            break;
                                        }
                                    }
                                }

                                // Also hide the element itself
                                element.style.display = 'none';
                                element.style.visibility = 'hidden';
                                element.style.height = '0';
                                element.style.width = '0';
                                element.style.overflow = 'hidden';
                                element.style.margin = '0';
                                element.style.padding = '0';
                                element.style.position = 'absolute';
                                element.style.left = '-9999px';
                            }
                        }
                    });

                    // Specifically target elements that EXACTLY match the format "Price: $23,000.00"
                    document.querySelectorAll('div, p, span, small').forEach(function(element) {
                        if (element.textContent &&
                            (element.textContent.trim() === 'Price: $23,000.00' ||
                             element.textContent.trim().match(/^Price:\s+\$[\d,]+\.\d{2}$/))) {

                            // Hide this element
                            element.style.display = 'none';
                            element.style.visibility = 'hidden';
                            element.style.height = '0';
                            element.style.width = '0';
                            element.style.overflow = 'hidden';
                            element.style.margin = '0';
                            element.style.padding = '0';
                            element.style.position = 'absolute';
                            element.style.left = '-9999px';

                            // Also try to hide its parent
                            if (element.parentElement) {
                                element.parentElement.style.display = 'none';
                                element.parentElement.style.visibility = 'hidden';
                                element.parentElement.style.height = '0';
                                element.parentElement.style.width = '0';
                                element.parentElement.style.overflow = 'hidden';
                                element.parentElement.style.margin = '0';
                                element.parentElement.style.padding = '0';
                                element.parentElement.style.position = 'absolute';
                                element.parentElement.style.left = '-9999px';
                            }
                        }
                    });
                };

                // Run immediately and then periodically to catch dynamically loaded content
                removeUnwantedPriceElements();
                overrideRenderOptionsHtml();
                removeAllPriceSections();

                // Set up intervals to keep checking
                setInterval(removeUnwantedPriceElements, 300);
                setInterval(overrideRenderOptionsHtml, 300);
                setInterval(removeAllPriceSections, 300);

                // Also run after a slight delay to catch any elements that might be loaded after initial page load
                setTimeout(removeAllPriceSections, 100);
                setTimeout(removeAllPriceSections, 500);
                setTimeout(removeAllPriceSections, 1000);
                setTimeout(removeAllPriceSections, 2000);

                // NUCLEAR OPTION: Directly modify the DOM structure to remove the price section
                const removePriceSectionFromDOM = function() {
                    // Find all elements that contain exactly "Price: $23,000.00"
                    const walker = document.createTreeWalker(
                        document.body,
                        NodeFilter.SHOW_TEXT,
                        {
                            acceptNode: function(node) {
                                return (node.nodeValue &&
                                       (node.nodeValue.trim() === 'Price: $23,000.00' ||
                                        node.nodeValue.trim().match(/^Price:\s+\$[\d,]+\.\d{2}$/)))
                                    ? NodeFilter.FILTER_ACCEPT
                                    : NodeFilter.FILTER_SKIP;
                            }
                        }
                    );

                    const textNodes = [];
                    let currentNode;
                    while (currentNode = walker.nextNode()) {
                        textNodes.push(currentNode);
                    }

                    // Remove each text node and its parent elements
                    textNodes.forEach(function(node) {
                        // Try to find the parent container that holds just this price
                        let parent = node.parentNode;
                        let foundContainer = false;

                        // Check up to 5 levels of parent elements
                        for (let i = 0; i < 5 && parent; i++) {
                            // If this parent contains only the price text or very little other content
                            if (parent.textContent &&
                                (parent.textContent.trim() === 'Price: $23,000.00' ||
                                 parent.textContent.trim().match(/^Price:\s+\$[\d,]+\.\d{2}$/))) {

                                // We found the container, remove it from the DOM
                                if (parent.parentNode) {
                                    parent.parentNode.removeChild(parent);
                                    foundContainer = true;
                                    break;
                                }
                            }
                            parent = parent.parentNode;
                        }

                        // If we couldn't find a suitable container, just remove the text node
                        if (!foundContainer && node.parentNode) {
                            node.parentNode.removeChild(node);
                        }
                    });
                };

                // Run the nuclear option immediately and after delays
                removePriceSectionFromDOM();
                setTimeout(removePriceSectionFromDOM, 300);
                setTimeout(removePriceSectionFromDOM, 800);
                setTimeout(removePriceSectionFromDOM, 1500);

                // Hide the upper subtotal section
                const hideUpperSubtotalSection = function() {
                    // Find all rows in the checkout page that are not inside the grand total section
                    const upperRows = document.querySelectorAll('.checkout-page .mt-2.p-2 > .row:not(.grand-total-section):not(.grand-total-section .row)');

                    upperRows.forEach(function(row) {
                        // Check if this is not inside the grand total section
                        if (!row.closest('.grand-total-section')) {
                            row.style.display = 'none';
                            row.style.visibility = 'hidden';
                            row.style.height = '0';
                            row.style.width = '0';
                            row.style.margin = '0';
                            row.style.padding = '0';
                            row.style.overflow = 'hidden';
                            row.style.position = 'absolute';
                            row.style.left = '-9999px';
                            row.style.opacity = '0';
                            row.style.pointerEvents = 'none';
                        }
                    });

                    // Also hide any element with class upper-subtotal-section
                    const upperSubtotalSections = document.querySelectorAll('.upper-subtotal-section');
                    upperSubtotalSections.forEach(function(section) {
                        section.style.display = 'none';
                        section.style.visibility = 'hidden';
                        section.style.height = '0';
                        section.style.width = '0';
                        section.style.margin = '0';
                        section.style.padding = '0';
                        section.style.overflow = 'hidden';
                        section.style.position = 'absolute';
                        section.style.left = '-9999px';
                        section.style.opacity = '0';
                        section.style.pointerEvents = 'none';
                    });
                };

                // Ensure the Grand Total section has the correct styling
                const ensureGrandTotalStyling = function() {
                    // Find the grand total section
                    const grandTotalSection = document.querySelector('.grand-total-section');
                    if (grandTotalSection) {
                        // Apply the styling
                        grandTotalSection.style.border = '2px solid #003366';
                        grandTotalSection.style.borderRadius = '8px';
                        grandTotalSection.style.padding = '15px';
                        grandTotalSection.style.backgroundColor = '#f8f9fa';
                        grandTotalSection.style.marginTop = '20px';
                        grandTotalSection.style.marginBottom = '10px';
                        grandTotalSection.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';

                        // Find the title
                        const title = grandTotalSection.querySelector('.grand-total-title');
                        if (!title) {
                            // If the title doesn't exist, create it
                            const titleElement = document.createElement('h5');
                            titleElement.className = 'grand-total-title mb-3';
                            titleElement.textContent = 'Grand Total';
                            titleElement.style.color = '#003366';
                            titleElement.style.fontWeight = 'bold';
                            titleElement.style.textAlign = 'center';
                            titleElement.style.marginTop = '0';
                            titleElement.style.marginBottom = '15px';
                            titleElement.style.fontSize = '18px';
                            titleElement.style.textTransform = 'uppercase';
                            titleElement.style.letterSpacing = '1px';

                            // Insert it at the beginning of the grand total section
                            grandTotalSection.insertBefore(titleElement, grandTotalSection.firstChild);
                        } else {
                            // Apply styling to the existing title
                            title.style.color = '#003366';
                            title.style.fontWeight = 'bold';
                            title.style.textAlign = 'center';
                            title.style.marginTop = '0';
                            title.style.marginBottom = '15px';
                            title.style.fontSize = '18px';
                            title.style.textTransform = 'uppercase';
                            title.style.letterSpacing = '1px';
                        }

                        // Style the total text
                        const totalText = grandTotalSection.querySelector('.total-text');
                        if (totalText) {
                            totalText.style.fontWeight = 'bold';
                            totalText.style.fontSize = '18px';
                            totalText.style.color = '#003366';
                            totalText.style.marginBottom = '0';
                        }
                    }
                };

                // Run immediately and periodically
                hideUpperSubtotalSection();
                ensureGrandTotalStyling();

                // Set up intervals to keep checking
                setInterval(hideUpperSubtotalSection, 300);
                setInterval(ensureGrandTotalStyling, 300);

                // Also run after a slight delay to catch any elements that might be loaded after initial page load
                setTimeout(hideUpperSubtotalSection, 100);
                setTimeout(hideUpperSubtotalSection, 500);
                setTimeout(hideUpperSubtotalSection, 1000);
                setTimeout(hideUpperSubtotalSection, 2000);

                // Find all product rows in the checkout page
                const productRows = document.querySelectorAll('.cart-item');

                productRows.forEach(function(row) {
                    // Find the price display in this row
                    const priceColumn = row.querySelector('.col-auto.text-end');

                    if (priceColumn) {
                        // Check if this product has both sale price and original price
                        const hasTwoPrices = priceColumn.querySelectorAll('p').length > 1;

                        if (hasTwoPrices) {
                            // Clear the column and add our improved price display
                            priceColumn.innerHTML = '';

                            // Get the prices from the data attributes or from the original elements
                            const salePrice = row.getAttribute('data-sale-price') || priceColumn.querySelector('.text-danger.fw-bold').textContent;
                            const originalPrice = row.getAttribute('data-original-price') || priceColumn.querySelector('del.text-muted').textContent;

                            // Create a new price display with clear sale/original price indication
                            const priceDisplay = document.createElement('div');
                            priceDisplay.className = 'checkout-price-display';
                            priceDisplay.innerHTML = `
                                <div class="sale-price" style="color: #ff6633; font-weight: bold; font-size: 16px;">${salePrice}</div>
                                <div class="original-price" style="text-decoration: line-through; color: #999; font-size: 14px;">Was: ${originalPrice}</div>
                            `;

                            priceColumn.appendChild(priceDisplay);
                        }
                    }

                    // Find and remove any "Price:" labels or sections
                    const quantityControl = row.querySelector('.ec-checkout-quantity');
                    if (quantityControl) {
                        // Look for any element that contains "Price:" text
                        const priceLabels = row.querySelectorAll('*');
                        priceLabels.forEach(function(element) {
                            if (element.textContent && element.textContent.includes('Price:')) {
                                element.style.display = 'none';
                                element.style.visibility = 'hidden';
                                element.style.height = '0';
                                element.style.width = '0';
                                element.style.overflow = 'hidden';
                                element.style.margin = '0';
                                element.style.padding = '0';
                            }
                        });

                        // Also look for any small elements that might contain product options with price
                        const optionPrices = row.querySelectorAll('.small, small, div, p, span');
                        optionPrices.forEach(function(element) {
                            if (element.textContent && element.textContent.includes('Price:')) {
                                element.style.display = 'none';
                                element.style.visibility = 'hidden';
                                element.style.height = '0';
                                element.style.width = '0';
                                element.style.overflow = 'hidden';
                                element.style.margin = '0';
                                element.style.padding = '0';
                            }
                        });

                        // Specifically target the product options section
                        const productOptions = row.querySelectorAll('.small.d-flex.justify-content-between');
                        productOptions.forEach(function(element) {
                            element.style.display = 'none';
                            element.style.visibility = 'hidden';
                            element.style.height = '0';
                            element.style.width = '0';
                            element.style.overflow = 'hidden';
                            element.style.margin = '0';
                            element.style.padding = '0';
                        });
                    }
                });
            }
        });
    </script>

    {!! Theme::footer() !!}

    @include('packages/theme::toast-notification')
    </body>

    </html>
