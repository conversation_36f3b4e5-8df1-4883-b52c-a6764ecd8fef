<?php

namespace Botble\PayStation\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Http\Responses\BaseHttpResponse;
use Botble\Payment\Enums\PaymentStatusEnum;
use Bo<PERSON>ble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Services\Abstracts\PayStationPaymentAbstract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PayStationDebugController extends BaseController
{
    public function debugOrderCreation(Request $request, BaseHttpResponse $response)
    {
        // If it's a web request (not AJAX), show the debug interface
        if (!$request->wantsJson() && !$request->ajax()) {
            return view('plugins/paystation::debug');
        }
        $debugInfo = [
            'timestamp' => now()->toDateTimeString(),
            'request_data' => $request->all(),
            'steps' => []
        ];

        // Step 1: Check if plugins are activated
        $debugInfo['steps']['1_plugin_check'] = $this->checkPluginActivation();

        // Step 2: Check database connections
        $debugInfo['steps']['2_database_check'] = $this->checkDatabaseConnection();

        // Step 3: Check PayStation configuration
        $debugInfo['steps']['3_paystation_config'] = $this->checkPayStationConfig();

        // Step 4: Check recent orders
        $debugInfo['steps']['4_recent_orders'] = $this->checkRecentOrders();

        // Step 5: Check recent payments
        $debugInfo['steps']['5_recent_payments'] = $this->checkRecentPayments();

        // Step 6: Test order lookup with sample invoice
        $sampleInvoice = $request->input('test_invoice', 'LV1389-1752255805-1752783970-74');
        $debugInfo['steps']['6_order_lookup_test'] = $this->testOrderLookup($sampleInvoice);

        // Step 7: Check PAYMENT_ACTION_PAYMENT_PROCESSED
        $debugInfo['steps']['7_payment_action_check'] = $this->checkPaymentAction();

        // Step 8: Test callback simulation
        if ($request->has('simulate_callback')) {
            $debugInfo['steps']['8_callback_simulation'] = $this->simulateCallback($request);
        }

        // Step 9: Check Laravel logs
        $debugInfo['steps']['9_log_analysis'] = $this->analyzeLogs();

        // Step 10: Generate recommendations
        $debugInfo['recommendations'] = $this->generateRecommendations($debugInfo);

        // Log the debug info
        Log::info('PayStation Debug Analysis', $debugInfo);

        return $response->setData($debugInfo);
    }

    private function checkPluginActivation()
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            // Check if payment plugin is activated
            $activatedPlugins = setting('activated_plugins', []);
            $result['details']['activated_plugins'] = $activatedPlugins;
            $result['details']['payment_plugin_active'] = in_array('payment', $activatedPlugins);
            $result['details']['paystation_plugin_active'] = in_array('paystation', $activatedPlugins);

            // Check if constants are defined
            $result['details']['payment_action_defined'] = defined('PAYMENT_ACTION_PAYMENT_PROCESSED');
            $result['details']['paystation_constant_defined'] = defined('PAYSTATION_PAYMENT_METHOD_NAME');

            if ($result['details']['payment_plugin_active'] && $result['details']['paystation_plugin_active']) {
                $result['status'] = 'success';
                $result['message'] = 'Both plugins are activated';
            } else {
                $result['status'] = 'error';
                $result['message'] = 'Required plugins not activated';
            }

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Error checking plugins: ' . $e->getMessage();
        }

        return $result;
    }

    private function checkDatabaseConnection()
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            // Test database connection
            DB::connection()->getPdo();
            $result['details']['connection'] = 'success';

            // Check if required tables exist
            $tables = ['ec_orders', 'payments'];
            foreach ($tables as $table) {
                $result['details']['tables'][$table] = DB::getSchemaBuilder()->hasTable($table);
            }

            $result['status'] = 'success';
            $result['message'] = 'Database connection successful';

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Database error: ' . $e->getMessage();
        }

        return $result;
    }

    private function checkPayStationConfig()
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            $merchantId = get_payment_setting('merchant_id', 'paystation');
            $password = get_payment_setting('password', 'paystation');
            $status = get_payment_setting('status', 'paystation');

            $result['details']['merchant_id_set'] = !empty($merchantId);
            $result['details']['password_set'] = !empty($password);
            $result['details']['status'] = $status;
            $result['details']['app_url'] = config('app.url');

            if ($merchantId && $password) {
                $result['status'] = 'success';
                $result['message'] = 'PayStation configuration looks good';
            } else {
                $result['status'] = 'error';
                $result['message'] = 'PayStation configuration incomplete';
            }

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Config check error: ' . $e->getMessage();
        }

        return $result;
    }

    private function checkRecentOrders()
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            $orders = DB::table('ec_orders')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['id', 'status', 'amount', 'token', 'is_finished', 'payment_id', 'created_at']);

            $result['details']['total_orders'] = DB::table('ec_orders')->count();
            $result['details']['recent_orders'] = $orders->toArray();
            $result['details']['orders_without_payment'] = DB::table('ec_orders')
                ->whereNull('payment_id')
                ->where('created_at', '>', now()->subDay())
                ->count();

            $result['status'] = 'success';
            $result['message'] = 'Found ' . $orders->count() . ' recent orders';

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Orders check error: ' . $e->getMessage();
        }

        return $result;
    }

    private function checkRecentPayments()
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            $payments = DB::table('payments')
                ->where('payment_channel', 'paystation')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['id', 'charge_id', 'status', 'amount', 'order_id', 'created_at']);

            $result['details']['total_paystation_payments'] = DB::table('payments')
                ->where('payment_channel', 'paystation')
                ->count();
            $result['details']['recent_payments'] = $payments->toArray();

            $result['status'] = 'success';
            $result['message'] = 'Found ' . $payments->count() . ' recent PayStation payments';

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Payments check error: ' . $e->getMessage();
        }

        return $result;
    }

    private function testOrderLookup($invoiceNumber)
    {
        $result = [
            'status' => 'checking',
            'details' => [
                'invoice_number' => $invoiceNumber
            ]
        ];

        try {
            // Extract order ID from invoice
            $invoiceParts = explode('-', $invoiceNumber);
            $orderId = end($invoiceParts);
            
            $result['details']['invoice_parts'] = $invoiceParts;
            $result['details']['extracted_order_id'] = $orderId;
            $result['details']['order_id_is_numeric'] = is_numeric($orderId);

            if (is_numeric($orderId)) {
                // Try to find the order
                $order = DB::table('ec_orders')->where('id', $orderId)->first();
                
                if ($order) {
                    $result['details']['order_found'] = true;
                    $result['details']['order_data'] = [
                        'id' => $order->id,
                        'status' => $order->status,
                        'amount' => $order->amount,
                        'token' => $order->token,
                        'is_finished' => $order->is_finished,
                        'payment_id' => $order->payment_id
                    ];
                    $result['status'] = 'success';
                    $result['message'] = 'Order lookup successful';
                } else {
                    $result['details']['order_found'] = false;
                    $result['status'] = 'error';
                    $result['message'] = 'Order not found in database';
                }
            } else {
                $result['status'] = 'error';
                $result['message'] = 'Invalid order ID extracted from invoice';
            }

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Order lookup error: ' . $e->getMessage();
        }

        return $result;
    }

    private function checkPaymentAction()
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            $result['details']['payment_action_defined'] = defined('PAYMENT_ACTION_PAYMENT_PROCESSED');
            $result['details']['do_action_function_exists'] = function_exists('do_action');
            
            if (defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
                $result['details']['payment_action_value'] = PAYMENT_ACTION_PAYMENT_PROCESSED;
            }

            if (defined('PAYMENT_ACTION_PAYMENT_PROCESSED') && function_exists('do_action')) {
                $result['status'] = 'success';
                $result['message'] = 'Payment action system is ready';
            } else {
                $result['status'] = 'error';
                $result['message'] = 'Payment action system not available';
            }

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Payment action check error: ' . $e->getMessage();
        }

        return $result;
    }

    private function simulateCallback(Request $request)
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            $status = $request->input('status', 'Successful');
            $invoiceNumber = $request->input('invoice_number', 'LV1389-1752255805-1752783970-74');
            $trxId = $request->input('trx_id', 'TEST123');

            $result['details']['simulation_params'] = [
                'status' => $status,
                'invoice_number' => $invoiceNumber,
                'trx_id' => $trxId
            ];

            // Simulate the callback logic
            $orderLookup = $this->testOrderLookup($invoiceNumber);
            $result['details']['order_lookup_result'] = $orderLookup;

            if ($orderLookup['status'] === 'success' && isset($orderLookup['details']['order_data'])) {
                $orderData = $orderLookup['details']['order_data'];
                
                // Test if we can trigger the payment action
                if (defined('PAYMENT_ACTION_PAYMENT_PROCESSED') && function_exists('do_action')) {
                    $paymentData = [
                        'amount' => $orderData['amount'],
                        'currency' => 'BDT',
                        'charge_id' => $trxId,
                        'payment_channel' => 'paystation',
                        'status' => PaymentStatusEnum::COMPLETED,
                        'order_id' => [$orderData['id']],
                    ];
                    
                    $result['details']['would_trigger_payment_action'] = true;
                    $result['details']['payment_action_data'] = $paymentData;
                    $result['status'] = 'success';
                    $result['message'] = 'Callback simulation successful - order would be processed';
                } else {
                    $result['status'] = 'error';
                    $result['message'] = 'Payment action not available - order would not be processed';
                }
            } else {
                $result['status'] = 'error';
                $result['message'] = 'Order lookup failed - callback would fail';
            }

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Callback simulation error: ' . $e->getMessage();
        }

        return $result;
    }

    private function analyzeLogs()
    {
        $result = [
            'status' => 'checking',
            'details' => []
        ];

        try {
            $logFile = storage_path('logs/laravel-' . date('Y-m-d') . '.log');
            
            if (file_exists($logFile)) {
                $result['details']['log_file_exists'] = true;
                $result['details']['log_file_path'] = $logFile;
                
                // Get recent PayStation log entries
                $command = "grep -i 'paystation' \"$logFile\" | tail -10";
                $output = shell_exec($command);
                
                if ($output) {
                    $result['details']['recent_paystation_logs'] = explode("\n", trim($output));
                } else {
                    $result['details']['recent_paystation_logs'] = [];
                }
                
                $result['status'] = 'success';
                $result['message'] = 'Log analysis completed';
            } else {
                $result['details']['log_file_exists'] = false;
                $result['status'] = 'warning';
                $result['message'] = 'Log file not found';
            }

        } catch (\Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Log analysis error: ' . $e->getMessage();
        }

        return $result;
    }

    private function generateRecommendations($debugInfo)
    {
        $recommendations = [];

        // Check plugin activation
        if (isset($debugInfo['steps']['1_plugin_check']['status']) && 
            $debugInfo['steps']['1_plugin_check']['status'] === 'error') {
            $recommendations[] = [
                'priority' => 'HIGH',
                'issue' => 'Plugins not activated',
                'solution' => 'Activate Payment and PayStation plugins in admin panel'
            ];
        }

        // Check payment action
        if (isset($debugInfo['steps']['7_payment_action_check']['details']['payment_action_defined']) && 
            !$debugInfo['steps']['7_payment_action_check']['details']['payment_action_defined']) {
            $recommendations[] = [
                'priority' => 'HIGH',
                'issue' => 'PAYMENT_ACTION_PAYMENT_PROCESSED not defined',
                'solution' => 'Ensure Payment plugin is properly activated and loaded'
            ];
        }

        // Check PayStation config
        if (isset($debugInfo['steps']['3_paystation_config']['status']) && 
            $debugInfo['steps']['3_paystation_config']['status'] === 'error') {
            $recommendations[] = [
                'priority' => 'HIGH',
                'issue' => 'PayStation configuration incomplete',
                'solution' => 'Configure Merchant ID and Password in PayStation settings'
            ];
        }

        // Check order lookup
        if (isset($debugInfo['steps']['6_order_lookup_test']['status']) && 
            $debugInfo['steps']['6_order_lookup_test']['status'] === 'error') {
            $recommendations[] = [
                'priority' => 'MEDIUM',
                'issue' => 'Order lookup failing',
                'solution' => 'Check if orders are being created during checkout process'
            ];
        }

        // Check callback simulation
        if (isset($debugInfo['steps']['8_callback_simulation']['status']) && 
            $debugInfo['steps']['8_callback_simulation']['status'] === 'error') {
            $recommendations[] = [
                'priority' => 'HIGH',
                'issue' => 'Callback processing would fail',
                'solution' => 'Fix the issues identified in order lookup and payment action checks'
            ];
        }

        return $recommendations;
    }
}
