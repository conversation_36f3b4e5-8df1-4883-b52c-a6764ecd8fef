(function ($) {
    'use strict';

    $(document).ready(function () {
        // Force the sticky bar to be visible at all times on mobile/tablet
        if ($(window).width() < 992) {
            $('.sticky-atc-wrap').addClass('sticky-atc-shown');

            $(window).on('scroll resize', function() {
                $('.sticky-atc-wrap').addClass('sticky-atc-shown');
            });
        }
        // Sync quantity between main form and sticky bar
        $(document).on('change', '.product-details-content .qty, .cart-form .qty', function() {
            $('.sticky-atc-wrap .qty').val($(this).val());
        });

        $(document).on('change', '.sticky-atc-wrap .qty', function() {
            $('.product-details-content .qty, .cart-form .qty').val($(this).val());
        });

        // Handle quantity buttons in sticky bar
        $(document).on('click', '.sticky-atc-wrap .decrease', function() {
            const $input = $(this).siblings('.qty');
            const currentVal = parseInt($input.val());
            const min = parseInt($input.attr('min'));

            if (currentVal > min) {
                $input.val(currentVal - 1).trigger('change');
            }
        });

        $(document).on('click', '.sticky-atc-wrap .increase', function() {
            const $input = $(this).siblings('.qty');
            const currentVal = parseInt($input.val());
            const max = parseInt($input.attr('max'));

            if (currentVal < max) {
                $input.val(currentVal + 1).trigger('change');
            }
        });

        // Handle add to cart and buy now buttons in sticky bar
        $(document).on('click', '.sticky-atc-wrap .add-to-cart-button', function() {
            const $this = $(this);
            const qty = $('.sticky-atc-wrap .qty').val();

            // Update the main form quantity
            $('.product-details-content .qty, .cart-form .qty').val(qty);

            // Trigger the main form button click
            const buttonName = $this.attr('name');
            const $mainButton = $('.product-details-content .add-to-cart-button[name="' + buttonName + '"], .cart-form .add-to-cart-button[name="' + buttonName + '"]');

            if ($mainButton.length) {
                $mainButton.first().trigger('click');
            } else {
                // If button not found, submit the form directly
                $('.cart-form').submit();
            }
        });
    });
})(jQuery);
