<?php

echo "PayStation Callback Debug Script\n";
echo "================================\n\n";

// Load Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "1. Checking if callback route is accessible:\n";

try {
    $router = app('router');
    $routes = $router->getRoutes();
    
    $callbackRoute = null;
    foreach ($routes as $route) {
        if (strpos($route->getName() ?? '', 'paystation.callback') !== false) {
            $callbackRoute = $route;
            break;
        }
    }
    
    if ($callbackRoute) {
        echo "   ✓ Callback route found: " . $callbackRoute->uri() . "\n";
        echo "   ✓ Route name: " . $callbackRoute->getName() . "\n";
        echo "   ✓ Methods: " . implode(', ', $callbackRoute->methods()) . "\n";
    } else {
        echo "   ✗ Callback route NOT found\n";
        echo "   This means PayStation can't send callbacks to your site\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking routes: " . $e->getMessage() . "\n";
}

echo "\n2. Checking Payment Records:\n";

try {
    // Check if there are any PayStation payment records
    $payments = \Botble\Payment\Models\Payment::query()
        ->where('payment_channel', 'paystation')
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    if ($payments->count() > 0) {
        echo "   ✓ Found " . $payments->count() . " PayStation payment records\n";
        foreach ($payments as $payment) {
            echo "     - Payment ID: {$payment->id}, Charge ID: {$payment->charge_id}, Status: {$payment->status}\n";
            echo "       Order ID: " . (is_array($payment->order_id) ? implode(',', $payment->order_id) : $payment->order_id) . "\n";
        }
    } else {
        echo "   ✗ No PayStation payment records found\n";
        echo "     This suggests payments are not being stored properly\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking payments: " . $e->getMessage() . "\n";
}

echo "\n3. Checking Order Records:\n";

try {
    // Check recent orders
    $orders = \Botble\Ecommerce\Models\Order::query()
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    if ($orders->count() > 0) {
        echo "   ✓ Found " . $orders->count() . " recent orders\n";
        foreach ($orders as $order) {
            echo "     - Order ID: {$order->id}, Status: {$order->status}, Amount: {$order->amount}\n";
            echo "       Token: {$order->token}, Is Finished: " . ($order->is_finished ? 'Yes' : 'No') . "\n";
            echo "       Payment ID: " . ($order->payment_id ?: 'None') . "\n";
        }
    } else {
        echo "   ✗ No orders found\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking orders: " . $e->getMessage() . "\n";
}

echo "\n4. Checking Constants and Actions:\n";

// Load PayStation constants
$constantsFile = 'platform/plugins/paystation/helpers/constants.php';
if (file_exists($constantsFile)) {
    include_once $constantsFile;
    
    if (defined('PAYSTATION_PAYMENT_METHOD_NAME')) {
        echo "   ✓ PAYSTATION_PAYMENT_METHOD_NAME: " . PAYSTATION_PAYMENT_METHOD_NAME . "\n";
    } else {
        echo "   ✗ PAYSTATION_PAYMENT_METHOD_NAME not defined\n";
    }
} else {
    echo "   ✗ PayStation constants file not found\n";
}

if (defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
    echo "   ✓ PAYMENT_ACTION_PAYMENT_PROCESSED: " . PAYMENT_ACTION_PAYMENT_PROCESSED . "\n";
} else {
    echo "   ✗ PAYMENT_ACTION_PAYMENT_PROCESSED not defined\n";
    echo "     Payment plugin is not activated!\n";
}

echo "\n5. Simulating Callback Processing:\n";

// Let's simulate what happens in the callback
echo "   Testing callback logic with sample data...\n";

$sampleInvoice = 'LV1389-1752255805-1752783970-74'; // From your logs
$sampleStatus = 'Successful';
$sampleTrxId = 'TEST123';

echo "   Sample Invoice: $sampleInvoice\n";

// Try to find payment record
try {
    $payment = \Botble\Payment\Models\Payment::query()
        ->where('charge_id', $sampleInvoice)
        ->first();
    
    if ($payment) {
        echo "   ✓ Found payment record for invoice\n";
        $orderIds = is_array($payment->order_id) ? $payment->order_id : [$payment->order_id];
        $order = \Botble\Ecommerce\Models\Order::query()->whereIn('id', $orderIds)->first();
        
        if ($order) {
            echo "   ✓ Found order from payment record: Order ID {$order->id}\n";
        } else {
            echo "   ✗ Order not found from payment record\n";
        }
    } else {
        echo "   ✗ No payment record found for invoice\n";
        
        // Try fallback method
        $invoiceParts = explode('-', $sampleInvoice);
        $orderId = end($invoiceParts);
        echo "   Trying fallback: extracted order ID $orderId\n";
        
        $order = \Botble\Ecommerce\Models\Order::query()->find($orderId);
        if ($order) {
            echo "   ✓ Found order using fallback method: Order ID {$order->id}\n";
        } else {
            echo "   ✗ Order not found using fallback method either\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Error in callback simulation: " . $e->getMessage() . "\n";
}

echo "\n6. Checking Laravel Logs:\n";

$logFile = 'storage/logs/laravel-' . date('Y-m-d') . '.log';
if (file_exists($logFile)) {
    echo "   ✓ Log file exists: $logFile\n";
    
    // Get last 20 lines that contain PayStation
    $command = "tail -100 \"$logFile\" | grep -i paystation | tail -10";
    $output = shell_exec($command);
    
    if ($output) {
        echo "   Recent PayStation log entries:\n";
        $lines = explode("\n", trim($output));
        foreach ($lines as $line) {
            if (trim($line)) {
                echo "     " . trim($line) . "\n";
            }
        }
    } else {
        echo "   No recent PayStation log entries found\n";
    }
} else {
    echo "   ✗ Log file not found: $logFile\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "DIAGNOSIS:\n";
echo "==========\n";

if (!defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
    echo "❌ CRITICAL: Payment plugin is NOT activated\n";
    echo "   Solution: Go to Admin Panel > Plugins and activate Payment plugin\n\n";
}

echo "💡 LIKELY ISSUES:\n";
echo "1. Payment plugin not activated (most likely)\n";
echo "2. PayStation callback URL not configured in PayStation dashboard\n";
echo "3. Callback URL not publicly accessible\n";
echo "4. Orders being created but not linked to payments\n";
echo "5. PayStation not sending callbacks after payment\n";

echo "\n🔧 IMMEDIATE ACTIONS:\n";
echo "1. Activate Payment plugin in admin panel\n";
echo "2. Activate PayStation plugin in admin panel\n";
echo "3. Configure callback URL in PayStation dashboard:\n";
echo "   https://yourdomain.com/payment/paystation/callback\n";
echo "4. Test with a small amount and monitor logs\n";

echo "\nDebug completed!\n";
