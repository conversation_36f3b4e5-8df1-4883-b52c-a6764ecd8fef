<?php

namespace Botble\PayStation\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Services\Abstracts\PayStationPaymentAbstract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayStationController extends BaseController
{
    public function callback(Request $request, BaseHttpResponse $response)
    {
        // According to official documentation, callback receives URL parameters:
        // ?status=Successful&invoice_number=2021252525&trx_id=10XB9900
        $status = $request->input('status');
        $invoiceNumber = $request->input('invoice_number');
        $trxId = $request->input('trx_id');

        Log::info('PayStation: Callback received', [
            'status' => $status,
            'invoice_number' => $invoiceNumber,
            'trx_id' => $trxId,
            'all_params' => $request->all()
        ]);

        if (empty($status) || empty($invoiceNumber)) {
            Log::error('PayStation Gateway: Invalid callback request', $request->all());
            return response('Invalid request', 400);
        }

        // Extract Order ID from invoice_number (like SSLCommerz extracts from value_a)
        // Invoice format: LV{merchantId}-{timestamp}-{orderId}
        $invoiceParts = explode('-', $invoiceNumber);
        $orderId = end($invoiceParts);

        if (!$orderId || !is_numeric($orderId)) {
            Log::error("PayStation Gateway: Could not extract valid order ID from invoice: {$invoiceNumber}");
            return response('Invalid invoice number', 400);
        }

        // Find order directly (like SSLCommerz does)
        $order = \Botble\Ecommerce\Models\Order::query()->find($orderId);

        if (!$order) {
            Log::error("PayStation Gateway: Order not found for ID: {$orderId} from invoice: {$invoiceNumber}");
            return response('Order not found', 404);
        }

        Log::info("PayStation: Found order {$order->id} for invoice {$invoiceNumber}");

        $status = strtolower($status);

        // Check transaction status with PayStation API
        $paymentService = new class extends PayStationPaymentAbstract {
            public function makePayment(Request $request) { return true; }
            public function afterMakePayment(Request $request) { return true; }
        };

        $transactionStatus = $paymentService->checkTransactionStatus($invoiceNumber);

        $redirectUrl = null;

        Log::info('PayStation: Transaction status check result', $transactionStatus);

        // According to official API documentation, possible trx_status values:
        // processing, success, failed, refund
        // And callback status values: Successful/Failed/Canceled

        $isSuccessful = false;

        // First check the callback status parameter
        if (strtolower($status) === 'successful') {
            $isSuccessful = true;
        }



        // Then verify with transaction status API if available
        if ($transactionStatus['success'] && isset($transactionStatus['data']['trx_status'])) {
            $apiStatus = strtolower($transactionStatus['data']['trx_status']);
            // Fix: PayStation API returns "successful" not "success"
            $isSuccessful = ($apiStatus === 'success' || $apiStatus === 'successful');


        }



        if ($isSuccessful) {
            // Check if payment action is available
            if (!defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
                Log::error("PayStation: PAYMENT_ACTION_PAYMENT_PROCESSED not defined - Payment plugin not activated");
                return response('Payment system not available', 500);
            }

            if (!function_exists('do_action')) {
                Log::error("PayStation: do_action function not available");
                return response('Action system not available', 500);
            }

            // Payment successful - trigger the payment processed action (exactly like SSLCommerz)
            $paymentData = [
                'amount' => $order->amount,
                'currency' => 'BDT',
                'charge_id' => $trxId ?: $invoiceNumber,
                'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
                'customer_id' => $order->user_id,
                'customer_type' => $order->user_id ? get_class($order->user) : null,
                'payment_type' => 'direct',
                'order_id' => [$order->id],
            ];



            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, $paymentData);

            Log::info("PayStation: Payment processed action completed for order {$order->id}, transaction: {$trxId}");

            // Use PaymentHelper like SSLCommerz does
            return $response
                ->setNextUrl(PaymentHelper::getRedirectURL($order->token))
                ->setMessage(__('Checkout successfully!'));

        } else {
            // Payment failed or cancelled
            Log::info("PayStation: Payment failed/cancelled for order {$order->id}, status: {$status}");

            // Use PaymentHelper for cancel URL like SSLCommerz does
            return $response
                ->setError()
                ->setNextUrl(PaymentHelper::getCancelURL($order->token))
                ->setMessage(__('Payment failed!'));
        }
    }

    public function webhook(Request $request)
    {
        // Handle webhook notifications from PayStation
        Log::info('PayStation Webhook received', $request->all());
        
        // You can add webhook verification logic here if PayStation provides webhook signatures
        
        return response('OK', 200);
    }


}
