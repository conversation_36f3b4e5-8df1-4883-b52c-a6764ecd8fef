<?php

namespace Botble\PayStation\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Services\Abstracts\PayStationPaymentAbstract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayStationController extends BaseController
{
    public function callback(Request $request)
    {
        // According to official documentation, callback receives URL parameters:
        // ?status=Successful&invoice_number=2021252525&trx_id=10XB9900
        $status = $request->input('status');
        $invoiceNumber = $request->input('invoice_number');
        $trxId = $request->input('trx_id');

        Log::info('PayStation: Callback received', [
            'status' => $status,
            'invoice_number' => $invoiceNumber,
            'trx_id' => $trxId,
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'all_params' => $request->all(),
            'headers' => $request->headers->all()
        ]);

        if (empty($status) || empty($invoiceNumber)) {
            Log::error('PayStation Gateway: Invalid callback request', [
                'missing_status' => empty($status),
                'missing_invoice' => empty($invoiceNumber),
                'all_params' => $request->all()
            ]);
            return response('Invalid request', 400);
        }

        Log::info('PayStation: Starting order lookup', ['invoice_number' => $invoiceNumber]);

        // First try to find the payment record by charge_id (invoice_number)
        $payment = \Botble\Payment\Models\Payment::query()
            ->where('charge_id', $invoiceNumber)
            ->first();

        if ($payment) {
            Log::info('PayStation: Found payment record', [
                'payment_id' => $payment->id,
                'order_id' => $payment->order_id,
                'status' => $payment->status
            ]);

            // Get order from payment record
            $orderIds = is_array($payment->order_id) ? $payment->order_id : [$payment->order_id];
            $order = \Botble\Ecommerce\Models\Order::query()->whereIn('id', $orderIds)->first();

            if ($order) {
                Log::info('PayStation: Found order from payment record', [
                    'order_id' => $order->id,
                    'order_status' => $order->status,
                    'is_finished' => $order->is_finished
                ]);
            }
        } else {
            Log::info('PayStation: No payment record found, trying fallback method');

            // Fallback: Extract Order ID from invoice_number: e.g., LV941-1741945382-33410
            $invoiceParts = explode('-', $invoiceNumber);
            $orderId = end($invoiceParts);

            Log::info('PayStation: Extracted order ID from invoice', [
                'invoice_parts' => $invoiceParts,
                'extracted_order_id' => $orderId
            ]);

            if (!$orderId) {
                Log::error("PayStation Gateway: Could not extract order ID from invoice: {$invoiceNumber}");
                return response('Invalid invoice number', 400);
            }

            $order = \Botble\Ecommerce\Models\Order::query()->find($orderId);

            if ($order) {
                Log::info('PayStation: Found order using fallback method', [
                    'order_id' => $order->id,
                    'order_status' => $order->status,
                    'is_finished' => $order->is_finished
                ]);
            }
        }

        if (!$order) {
            Log::error("PayStation Gateway: Order not found for invoice: {$invoiceNumber}", [
                'payment_found' => $payment ? true : false,
                'fallback_order_id' => $orderId ?? null
            ]);
            return response('Order not found', 404);
        }

        $status = strtolower($status);

        // Check transaction status with PayStation API
        $paymentService = new class extends PayStationPaymentAbstract {
            public function makePayment(Request $request) { return true; }
            public function afterMakePayment(Request $request) { return true; }
        };

        $transactionStatus = $paymentService->checkTransactionStatus($invoiceNumber);

        $redirectUrl = null;

        Log::info('PayStation: Transaction status check result', $transactionStatus);

        // According to official API documentation, possible trx_status values:
        // processing, success, failed, refund
        // And callback status values: Successful/Failed/Canceled

        $isSuccessful = false;

        // First check the callback status parameter
        if (strtolower($status) === 'successful') {
            $isSuccessful = true;
        }

        // Then verify with transaction status API if available
        if ($transactionStatus['success'] && isset($transactionStatus['data']['trx_status'])) {
            $apiStatus = strtolower($transactionStatus['data']['trx_status']);
            $isSuccessful = ($apiStatus === 'success');
        }

        if ($isSuccessful) {
            // Payment successful - trigger the payment processed action
            $paymentData = [
                'amount' => $order->amount,
                'currency' => 'BDT',
                'charge_id' => $trxId ?: $invoiceNumber,
                'order_id' => [$order->id],
                'customer_id' => $order->user_id,
                'customer_type' => $order->user_id ? get_class($order->user) : null,
                'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
                'payment_type' => 'direct',
            ];

            Log::info("PayStation: Triggering payment processed action", $paymentData);

            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, $paymentData);

            Log::info("PayStation: Payment processed action completed for order {$order->id}, transaction: {$trxId}");

            // Redirect to success page using the order token
            $redirectUrl = route('public.checkout.success', $order->token);

        } else {
            // Payment failed or cancelled
            Log::info("PayStation: Payment failed/cancelled for order {$order->id}, status: {$status}");

            $failUrl = get_payment_setting('fail_url', PAYSTATION_PAYMENT_METHOD_NAME);
            $redirectUrl = $failUrl ?: route('public.checkout.failure');
        }

        if ($redirectUrl) {
            return redirect($redirectUrl);
        }

        return response('Payment processed', 200);
    }

    public function webhook(Request $request)
    {
        // Handle webhook notifications from PayStation
        Log::info('PayStation Webhook received', $request->all());
        
        // You can add webhook verification logic here if PayStation provides webhook signatures
        
        return response('OK', 200);
    }


}
