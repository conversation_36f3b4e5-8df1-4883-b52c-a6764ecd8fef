<?php

namespace Botble\PayStation\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Services\Abstracts\PayStationPaymentAbstract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayStationController extends BaseController
{
    public function callback(Request $request)
    {
        $status = $request->input('status');
        $invoiceNumber = $request->input('invoice_number');
        $trxId = $request->input('trx_id');

        if (empty($status) || empty($invoiceNumber)) {
            Log::error('PayStation Gateway: Invalid callback request', $request->all());
            return response('Invalid request', 400);
        }

        // Extract Order ID from invoice_number: e.g., LV941-1741945382-33410
        $invoiceParts = explode('-', $invoiceNumber);
        $orderId = end($invoiceParts);

        if (!$orderId) {
            Log::error("PayStation Gateway: Could not extract order ID from invoice: {$invoiceNumber}");
            return response('Invalid invoice number', 400);
        }

        // Get the order using the ecommerce plugin
        if (is_plugin_active('ecommerce')) {
            $order = app(\Botble\Ecommerce\Repositories\Interfaces\OrderInterface::class)->findById($orderId);
            
            if (!$order) {
                Log::error("PayStation Gateway: Order not found for invoice: {$invoiceNumber}");
                return response('Order not found', 404);
            }
        } else {
            Log::error("PayStation Gateway: Ecommerce plugin not active");
            return response('Ecommerce plugin not active', 500);
        }

        $status = strtolower($status);

        // Check transaction status with PayStation API
        $paymentService = new class extends PayStationPaymentAbstract {
            public function makePayment(Request $request) { return true; }
            public function afterMakePayment(Request $request) { return true; }
        };

        $transactionStatus = $paymentService->checkTransactionStatus($invoiceNumber);

        $redirectUrl = null;

        if ($transactionStatus['success'] && 
            isset($transactionStatus['data']['trx_status']) && 
            $transactionStatus['data']['trx_status'] === 'successful') {
            
            // Payment successful
            $this->processSuccessfulPayment($order, $trxId, $invoiceNumber);
            $redirectUrl = $order->checkout_order_received_url ?? route('public.checkout.success');
            
        } else {
            // Payment failed or cancelled
            $this->processFailedPayment($order, $status, $invoiceNumber);
            
            $failUrl = get_payment_setting('fail_url', PAYSTATION_PAYMENT_METHOD_NAME);
            $redirectUrl = $failUrl ?: route('public.checkout.failure');
        }

        if ($redirectUrl) {
            return redirect($redirectUrl);
        }

        return response('Payment processed', 200);
    }

    public function webhook(Request $request)
    {
        // Handle webhook notifications from PayStation
        Log::info('PayStation Webhook received', $request->all());
        
        // You can add webhook verification logic here if PayStation provides webhook signatures
        
        return response('OK', 200);
    }

    private function processSuccessfulPayment($order, $trxId, $invoiceNumber)
    {
        try {
            // Update payment status
            if ($order->payment) {
                $order->payment->update([
                    'status' => PaymentStatusEnum::COMPLETED,
                    'charge_id' => $trxId,
                ]);
            }

            // Update order status
            $order->update(['status' => 'completed']);

            // Add order note
            if (method_exists($order, 'addNote')) {
                $order->addNote("Payment completed via PayStation. Transaction ID: {$trxId}");
            }

            // Store payment information
            PaymentHelper::storeLocalPayment([
                'amount' => $order->amount,
                'currency' => 'BDT',
                'charge_id' => $trxId,
                'order_id' => [$order->id],
                'customer_id' => $order->user_id,
                'customer_type' => get_class($order->user ?? new \stdClass()),
                'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
            ]);

            Log::info("PayStation: Payment completed for order {$order->id}, transaction: {$trxId}");

        } catch (\Exception $e) {
            Log::error("PayStation: Error processing successful payment for order {$order->id}: " . $e->getMessage());
        }
    }

    private function processFailedPayment($order, $status, $invoiceNumber)
    {
        try {
            $orderStatus = $status === 'canceled' ? 'cancelled' : 'failed';
            $paymentStatus = $status === 'canceled' ? PaymentStatusEnum::FAILED : PaymentStatusEnum::FAILED;

            // Update payment status
            if ($order->payment) {
                $order->payment->update([
                    'status' => $paymentStatus,
                ]);
            }

            // Update order status
            $order->update(['status' => $orderStatus]);

            // Add order note
            if (method_exists($order, 'addNote')) {
                $order->addNote("PayStation payment {$status}. Invoice: {$invoiceNumber}");
            }

            Log::info("PayStation: Payment {$status} for order {$order->id}, invoice: {$invoiceNumber}");

        } catch (\Exception $e) {
            Log::error("PayStation: Error processing failed payment for order {$order->id}: " . $e->getMessage());
        }
    }
}
