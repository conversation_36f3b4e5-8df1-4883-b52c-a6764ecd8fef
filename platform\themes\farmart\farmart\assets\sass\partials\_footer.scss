.footer-mobile {
    overflow: hidden;
    position: fixed;
    bottom: -1px;
    left: 0;
    width: 100vw;
    z-index: 1000;
    box-shadow: 0 0 4px rgb(0 0 0 / 10%);

    .menu--footer {
        display: flex;
        align-items: center;
        background: #fff;
        list-style: none;
        margin: 0;
        padding: 0;

        li {
            flex-grow: 1;
            padding: 5px 0 !important;
            position: relative;

            a {
                display: block;
                text-align: center;
                z-index: 1;
                position: relative;
                padding: 0.25rem 1rem;
                color: #000;

                i {
                    display: inline-block;
                    font-size: 20px;
                }

                span {
                    color: #000;
                    font-size: 12px;
                }

                .icon-cart {
                    position: relative;

                    .cart-counter {
                        position: absolute;
                        right: -10px;
                        top: -6px;
                        display: block;
                        text-align: center;
                        background-color: $color-primary;
                        color: #fff;
                        border-radius: 3px;
                        font-size: 10px;
                        font-weight: 700;
                        padding: 5px 5px 3px;
                        line-height: 1;
                        min-width: 18px;
                    }
                }
            }
        }
    }
}

#footer {
    margin-bottom: 81px;
    color: $footer-text-color;

    h2, h3, h4, h5, h6 {
        color: $footer-heading-color;
    }

    .border-top {
        border-top: 1px solid $footer-border-color !important;
    }

    .widget {
        font-size: 16px;

        .widget-title {
            font-size: 18px;
        }

        &.widget-custom-menu {
            ul {
                li {
                    padding-top: 0.5rem !important;
                    padding-bottom: 0.5rem !important;

                    a {
                        padding-top: 0.5rem !important;
                        padding-bottom: 0.5rem !important;
                    }
                }
            }
        }
    }

    ul {
        list-style-type: none;

        li {
            a {
                color: $footer-text-color;

                &:hover {
                    color: $footer-hover-color;
                }
            }
        }
    }

    .subscribe-form {
        .input-group {
            height: 50px;

            .input-group-text {
                background-color: transparent;
                border: 1px solid $footer-border-color;
                border-right: 0;
                padding-left: 1.5em;
            }

            input {
                border-left: 0;
                border-right: 0;
                padding: .375rem .75rem;
            }
        }

        input {
            border-color: $footer-border-color;
        }

        button {
            background-color: var(--primary-button-background-color);
            color: $primary-button-color;
            border: 0;
            font-size: 14px;
            font-weight: 700;
            min-width: 130px;
        }
    }

}

.site-info__item {
    .site-info__image {
        img {
            max-width: 100%;
            height: 37px;
        }
    }

    .site-info__title {
        font-size: 18px;
        letter-spacing: -1px;
        color: $footer-heading-color;
        margin: 0 0 3px;
    }
}

@media screen and (min-width: 1024px) {
    .footer-mobile {
        display: none;
    }
    #footer {
        margin-bottom: 0;
    }
}

.footer-socials-container {
    ul {
        li {
            a {
                border-radius: 3px;
                padding: 4px;
                background-color: var(--primary-button-background-color);

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }
}

.footer__links {
    padding: 30px 0;
    border-top: 1px solid $footer-border-color;

    .container-xxxl {
        div {
            &:last-child {
                p {
                    margin-bottom: 0;
                }
            }
        }
    }

    p {

        strong {
            color: $footer-heading-color;
            margin-right: 20px;
            font-weight: 700;
        }

        a {
            display: inline-block;
            color: $footer-text-color;
            line-height: 20px;
            margin-right: 10px;
            position: relative;

            &:after {
                content: '|';
                margin-left: 5px;
            }

            &:before {
                content: '';
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: $color-heading;
                @include transform(scale3d(0, 1, 1));
                transition: transform 0.4s $timing-1;
                @include transform-origin(100% 50%);
            }

            &:hover {
                color: $footer-hover-color;

                &:before {
                    @include transform(scale3d(1, 1, 1));
                    @include transform-origin(0 50%);
                }
            }

            &:last-child {
                &:after {
                    display: none;
                }
            }
        }
    }
}

.footer-socials {
    align-items: center;
}
