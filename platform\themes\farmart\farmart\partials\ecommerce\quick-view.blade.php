<div class="container product-detail-container">
    <div class="row">
        <div class="col-md-6">
            <div class="px-1 py-1 px-lg-5 py-lg-5 py-md-1 my-lg-5 my-md-1 my-2 mb-4 position-relative">
                @include('theme.farmart::partials.ecommerce.custom-quick-view-gallery')
                @if (EcommerceHelper::isWishlistEnabled())
                    <div class="wishlist-button product-wishlist-button" style="position: absolute; top: 15px; right: 15px; z-index: 10;">
                        <a
                            class="wishlist btn btn-sm rounded-circle @if (!empty($wishlistIds) && in_array($product->id, $wishlistIds)) added-to-wishlist @endif"
                            data-url="{{ route('public.wishlist.add', $product->id) }}"
                            href="#"
                            title="{{ __('Add to Wishlist') }}"
                            style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border: none; background-color: rgba(0, 0, 0, 0.15); color: #fff; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); position: relative;"
                        >
                            <span class="svg-icon" style="width: 20px; height: 20px;">
                                <svg>
                                    <use
                                        href="#svg-icon-wishlist"
                                        xlink:href="#svg-icon-wishlist"
                                    ></use>
                                </svg>
                            </span>
                        </a>
                    </div>
                @endif
            </div>
        </div>
        <div class="col-md-6">
            <div class="product-modal-entry product-details js-product-content">
                <div class="entry-product-header">
                    <div class="product-header-left">
                        <h2 class="h3 product_title entry-title"><a href="{{ $product->url }}">{{ $product->name }}</a>
                        </h2>
                        <div class="product-entry-meta">
                            @if ($product->brand_id)
                                <p class="mb-0 me-2 pe-2 text-secondary">{{ __('Brand') }}: <a
                                        href="{{ $product->brand->url }}"
                                    >{{ $product->brand->name }}</a></p>
                            @endif

                            @if (EcommerceHelper::isReviewEnabled())
                                <div class="col-auto">
                                    {!! Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]) !!}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                {!! Theme::partial('ecommerce.product-price', compact('product')) !!}

                @if (is_plugin_active('marketplace') && $product->store_id)
                    <div class="product-meta-sold-by my-2">
                        <span class="d-inline-block">{{ __('Sold By') }}: </span>
                        <a href="{{ $product->store->url }}">
                            {{ $product->store->name }}
                        </a>
                    </div>
                @endif

                {!! Theme::partial('ecommerce.product-availability', compact('product', 'productVariation')) !!}

                <div class="product-details__short-description">
                    {!! apply_filters('ecommerce_before_product_description', null, $product) !!}
                    {!! BaseHelper::clean($product->description) !!}
                    {!! apply_filters('ecommerce_after_product_description', null, $product) !!}
                </div>
                {!! Theme::partial(
                    'ecommerce.product-cart-form',
                    compact('product', 'wishlistIds', 'selectedAttrs') + [
                        'withButtons' => true,
                        'withVariations' => true,
                        'withProductOptions' => true,
                        'withBuyNow' => true,
                    ],
                ) !!}

                <div class="meta-sku @if (!$product->sku) d-none @endif">
                    <span class="meta-label d-inline-block pe-2">{{ __('SKU') }}:</span>
                    <span class="meta-value">{{ $product->sku }}</span>
                </div>
                @if ($product->categories->isNotEmpty())
                    <div class="meta-categories">
                        <span class="meta-label d-inline-block pe-2">{{ __('Categories') }}:</span>
                        @foreach ($product->categories as $category)
                            <a href="{{ $category->url }}">{{ $category->name }}</a>
                            @if (!$loop->last)
                                ,
                            @endif
                        @endforeach
                    </div>
                @endif
                @if ($product->tags->isNotEmpty())
                    <div class="meta-categories">
                        <span class="meta-label d-inline-block pe-2">{{ __('Tags') }}:</span>
                        @foreach ($product->tags as $tag)
                            <a href="{{ $tag->url }}">{{ $tag->name }}</a>
                            @if (!$loop->last)
                                ,
                            @endif
                        @endforeach
                    </div>
                @endif

            </div>
            @if (theme_option('social_share_enabled', 'yes') == 'yes')
                <div class="mt-5">
                    {!! Theme::partial('share-socials', compact('product')) !!}
                </div>
            @endif
        </div>
    </div>
</div>


